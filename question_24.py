"""
问题24：列出2021年所有发货量超过1000件的仓库，包含仓库名称和发货量
"""

from data_utils import create_processor

def solve_question_24():
    """解决问题24：列出2021年所有发货量超过1000件的仓库，包含仓库名称和发货量"""

    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()

    # 按仓库统计发货量
    warehouse_quantity = processor.get_top_by_column(
        df,
        group_by='仓库名称',
        agg_column='数量',
        agg_func='sum',
        top_n=50  # 先获取更多数据用于筛选
    )

    # 重命名列
    warehouse_quantity.columns = ['仓库名称', '发货量']

    # 筛选发货量超过1000件的仓库
    filtered_warehouses = warehouse_quantity[warehouse_quantity['发货量'] > 1000]

    # 输出结果
    processor.format_output(filtered_warehouses, "发货量超过1000件的仓库")

    # 统计信息
    total_qualified = len(filtered_warehouses)
    print(f"\n**统计信息：**")
    print(f"- 发货量超过1000件的仓库总数：{total_qualified} 个")

    if len(filtered_warehouses) > 0:
        print(f"- 最高发货量：{filtered_warehouses.iloc[0]['发货量']} 件（{filtered_warehouses.iloc[0]['仓库名称']}）")
        print(f"- 最低发货量：{filtered_warehouses.iloc[-1]['发货量']} 件（{filtered_warehouses.iloc[-1]['仓库名称']}）")
        print(f"- 平均发货量：{filtered_warehouses['发货量'].mean():.0f} 件")
        print(f"- 总发货量：{filtered_warehouses['发货量'].sum()} 件")

    # 额外统计信息 - 显示各仓库的详细信息
    import pandas as pd
    stats_data = []
    for _, row in filtered_warehouses.iterrows():
        warehouse = row['仓库名称']
        warehouse_data = df[df['仓库名称'] == warehouse]
        stats_data.append({
            '仓库名称': warehouse,
            '发货量': row['发货量'],
            '销售金额': warehouse_data['销售金额'].sum().round(2),
            '订单数量': len(warehouse_data),
            '平均单价': warehouse_data['折后单价'].mean().round(2),
            '学校数量': warehouse_data['学校'].nunique(),
            '小类数量': warehouse_data['小类'].nunique(),
            '主要学校': warehouse_data['学校'].mode().iloc[0] if len(warehouse_data['学校'].mode()) > 0 else 'N/A',
            '主要小类': warehouse_data['小类'].mode().iloc[0] if len(warehouse_data['小类'].mode()) > 0 else 'N/A'
        })

    detailed_stats = pd.DataFrame(stats_data)
    processor.format_output(detailed_stats, "各仓库详细统计信息")

    # 仓库发货分析
    total_warehouses = len(warehouse_quantity)
    print(f"\n**仓库发货分析：**")
    print(f"- 总仓库数：{total_warehouses} 个")
    print(f"- 超过1000件的仓库数：{total_qualified} 个")
    print(f"- 占比：{total_qualified/total_warehouses*100:.1f}%")
    print(f"- 这些仓库的总发货量：{filtered_warehouses['发货量'].sum()} 件")
    print(f"- 占总发货量比例：{filtered_warehouses['发货量'].sum()/df['数量'].sum()*100:.1f}%")

if __name__ == "__main__":
    solve_question_24()
