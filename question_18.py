"""
问题18：2021年所有校服中，哪个SKU的退货率（退货件数/总销售件数）最高？具体数值是多少？
"""

from data_utils import create_processor

def solve_question_18():
    """解决问题18：退货率最高的SKU"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
    # 计算各SKU的退货率
    sku_return_rate = processor.calculate_return_rate_by_quantity(df, 'SKU')
    
    # 筛选有足够样本量的SKU（至少10件）
    sku_return_rate = sku_return_rate[sku_return_rate['总发货件数'] >= 10]
    
    # 输出结果
    processor.format_output(sku_return_rate.head(15), "各SKU退货率排名（前15名，最少10件样本）")
    
    # 输出退货率最高的SKU信息
    if len(sku_return_rate) > 0:
        top_sku = sku_return_rate.iloc[0]
        print(f"\n**答案：SKU {top_sku['SKU']} 的退货率最高，具体数值是 {top_sku['退货率(%)']}%**")
        
        # 获取该SKU的详细信息
        sku_data = df[df['SKU'] == top_sku['SKU']]
        if len(sku_data) > 0:
            print(f"- 商品名称：{sku_data['商品名称'].iloc[0]}")
            print(f"- 所属学校：{sku_data['学校'].iloc[0]}")
            print(f"- 小类：{sku_data['小类'].iloc[0]}")
            print(f"- 季节：{sku_data['季节'].iloc[0]}")
    else:
        print("\n**没有找到符合条件的SKU（至少10件样本）**")
        return
    
    # 额外统计信息 - 显示各SKU的详细信息
    import pandas as pd
    stats_data = []
    for _, row in sku_return_rate.head(10).iterrows():  # 只显示前10个的详细信息
        sku = row['SKU']
        sku_data = df[df['SKU'] == sku]
        stats_data.append({
            'SKU': sku,
            '退货率(%)': row['退货率(%)'],
            '总发货件数': row['总发货件数'],
            '退货件数': row['退货件数'],
            '销售金额': sku_data['销售金额'].sum().round(2),
            '订单数量': len(sku_data),
            '平均单价': sku_data['折后单价'].mean().round(2),
            '学校': sku_data['学校'].iloc[0] if len(sku_data) > 0 else 'N/A',
            '小类': sku_data['小类'].iloc[0] if len(sku_data) > 0 else 'N/A',
            '季节': sku_data['季节'].iloc[0] if len(sku_data) > 0 else 'N/A'
        })
    
    detailed_stats = pd.DataFrame(stats_data)
    processor.format_output(detailed_stats, "前10个SKU详细统计信息")
    
    # 退货率分析
    print(f"\n**退货率分析：**")
    print(f"- 参与统计SKU数：{len(sku_return_rate)} 个")
    print(f"- 最高退货率：{sku_return_rate.iloc[0]['退货率(%)']}%")
    print(f"- 最低退货率：{sku_return_rate.iloc[-1]['退货率(%)']}%")
    print(f"- 平均退货率：{sku_return_rate['退货率(%)'].mean():.2f}%")
    
    # 退货率分布分析
    print(f"\n**退货率分布分析：**")
    rate_ranges = [
        (0, 5, "0-5%"),
        (5, 10, "5-10%"),
        (10, 20, "10-20%"),
        (20, 50, "20-50%"),
        (50, 100, "50%以上")
    ]
    
    for min_rate, max_rate, label in rate_ranges:
        count = len(sku_return_rate[(sku_return_rate['退货率(%)'] >= min_rate) & (sku_return_rate['退货率(%)'] < max_rate)])
        percentage = (count / len(sku_return_rate) * 100) if len(sku_return_rate) > 0 else 0
        print(f"- {label}：{count} 个SKU ({percentage:.1f}%)")

if __name__ == "__main__":
    solve_question_18()
