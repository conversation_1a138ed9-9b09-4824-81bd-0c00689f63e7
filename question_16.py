"""
问题16：2021年所有校服中，哪个季节的校服销售额占比最高？具体数值是多少？
"""

from data_utils import create_processor

def solve_question_16():
    """解决问题16：销售额占比最高的季节"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
    # 计算各季节销售额占比
    season_percentage = processor.calculate_percentage(
        df, 
        group_by='季节', 
        value_column='销售金额'
    )
    
    # 重命名列
    season_percentage.columns = ['季节', '销售金额', '占比(%)']
    season_percentage['销售金额'] = season_percentage['销售金额'].round(2)
    
    # 输出结果
    processor.format_output(season_percentage, "各季节校服销售额占比排名")
    
    # 输出占比最高的季节信息
    top_season = season_percentage.iloc[0]
    print(f"\n**答案：{top_season['季节']} 季节的校服销售额占比最高，具体数值是 {top_season['占比(%)']}%**")
    
    # 详细分析
    print(f"\n**详细分析：**")
    print(f"- 该季节销售金额：{top_season['销售金额']:.2f} 元")
    print(f"- 占比：{top_season['占比(%)']}%")
    
    # 额外统计信息 - 显示各季节的详细信息
    import pandas as pd
    stats_data = []
    for _, row in season_percentage.iterrows():
        season = row['季节']
        season_data = df[df['季节'] == season]
        stats_data.append({
            '季节': season,
            '销售金额': row['销售金额'],
            '占比(%)': row['占比(%)'],
            '订单数量': len(season_data),
            '发货数量': season_data['数量'].sum(),
            '平均单价': season_data['折后单价'].mean().round(2),
            '平均折扣率(%)': season_data['折扣率'].mean().round(2),
            '小类数量': season_data['小类'].nunique(),
            '学校数量': season_data['学校'].nunique(),
            '主要小类': season_data['小类'].mode().iloc[0] if len(season_data['小类'].mode()) > 0 else 'N/A'
        })
    
    detailed_stats = pd.DataFrame(stats_data)
    processor.format_output(detailed_stats, "各季节详细统计信息")
    
    # 季节销售分析
    total_sales = season_percentage['销售金额'].sum()
    print(f"\n**季节销售分析：**")
    print(f"- 总销售金额：{total_sales:.2f} 元")
    print(f"- 季节数量：{len(season_percentage)} 个")
    print(f"- 最高占比季节：{top_season['季节']} ({top_season['占比(%)']}%)")
    print(f"- 最低占比季节：{season_percentage.iloc[-1]['季节']} ({season_percentage.iloc[-1]['占比(%)']}%)")
    
    # 占比分布可视化（文字版）
    print(f"\n**销售额占比分布：**")
    for _, row in season_percentage.iterrows():
        season = row['季节']
        percentage = row['占比(%)']
        bar_length = int(percentage / 5)  # 每5%一个字符
        bar = '█' * bar_length
        print(f"- {season:6s}: {bar} {percentage}%")

if __name__ == "__main__":
    solve_question_16()
