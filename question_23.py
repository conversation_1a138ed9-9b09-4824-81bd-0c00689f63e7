"""
问题23：2021年所有校服中，哪个SKU的销售额占比最高？具体数值是多少？
"""

from data_utils import create_processor

def solve_question_23():
    """解决问题23：2021年所有校服中，哪个SKU的销售额占比最高？具体数值是多少？"""

    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()

    # 计算各SKU销售额占比
    sku_percentage = processor.calculate_percentage(
        df,
        group_by='SKU',
        value_column='销售金额'
    )

    # 重命名列
    sku_percentage.columns = ['SKU', '销售金额', '占比(%)']
    sku_percentage['销售金额'] = sku_percentage['销售金额'].round(2)

    # 输出结果
    processor.format_output(sku_percentage.head(15), "各SKU销售额占比排名（前15名）")

    # 输出占比最高的SKU信息
    top_sku = sku_percentage.iloc[0]
    print(f"\n**答案：SKU {top_sku['SKU']} 的销售额占比最高，具体数值是 {top_sku['占比(%)']}%**")

    # 获取该SKU的详细信息
    sku_data = df[df['SKU'] == top_sku['SKU']]
    if len(sku_data) > 0:
        print(f"- 商品名称：{sku_data['商品名称'].iloc[0]}")
        print(f"- 所属学校：{sku_data['学校'].iloc[0]}")
        print(f"- 小类：{sku_data['小类'].iloc[0]}")
        print(f"- 季节：{sku_data['季节'].iloc[0]}")
        print(f"- 销售金额：{top_sku['销售金额']:.2f} 元")
        print(f"- 占比：{top_sku['占比(%)']}%")

    # 额外统计信息 - 显示各SKU的详细信息
    import pandas as pd
    stats_data = []
    for _, row in sku_percentage.head(10).iterrows():  # 只显示前10个的详细信息
        sku = row['SKU']
        sku_data = df[df['SKU'] == sku]
        stats_data.append({
            'SKU': sku,
            '销售金额': row['销售金额'],
            '占比(%)': row['占比(%)'],
            '订单数量': len(sku_data),
            '发货数量': sku_data['数量'].sum(),
            '平均单价': sku_data['折后单价'].mean().round(2),
            '平均折扣率(%)': sku_data['折扣率'].mean().round(2),
            '学校': sku_data['学校'].iloc[0] if len(sku_data) > 0 else 'N/A',
            '小类': sku_data['小类'].iloc[0] if len(sku_data) > 0 else 'N/A',
            '季节': sku_data['季节'].iloc[0] if len(sku_data) > 0 else 'N/A'
        })

    detailed_stats = pd.DataFrame(stats_data)
    processor.format_output(detailed_stats, "前10个SKU详细统计信息")

    # SKU销售分析
    total_sales = sku_percentage['销售金额'].sum()
    print(f"\n**SKU销售分析：**")
    print(f"- 总SKU数量：{len(sku_percentage)} 个")
    print(f"- 总销售金额：{total_sales:.2f} 元")
    print(f"- 最高占比SKU：{top_sku['SKU']} ({top_sku['占比(%)']}%)")
    print(f"- 前10个SKU占比总和：{sku_percentage.head(10)['占比(%)'].sum():.2f}%")
    print(f"- 前20个SKU占比总和：{sku_percentage.head(20)['占比(%)'].sum():.2f}%")

if __name__ == "__main__":
    solve_question_23()
