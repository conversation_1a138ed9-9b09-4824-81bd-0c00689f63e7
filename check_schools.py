"""
检查数据中的学校名称
"""

from data_utils import create_processor

def main():
    processor = create_processor()
    df = processor.get_merged_data()
    
    print("学校名称样例:")
    schools = df['学校'].unique()
    for i, school in enumerate(schools[:30]):
        print(f"{i+1:2d}. {school}")
    
    print(f"\n总学校数: {df['学校'].nunique()}")
    
    # 检查是否有包含"海淀"的学校
    haidian_schools = [s for s in schools if '海淀' in str(s)]
    print(f"\n包含'海淀'的学校: {haidian_schools}")
    
    # 检查是否有包含"进修"的学校
    jinxiu_schools = [s for s in schools if '进修' in str(s)]
    print(f"\n包含'进修'的学校: {jinxiu_schools}")
    
    # 检查季节信息
    print(f"\n季节类型: {df['季节'].unique()}")

if __name__ == "__main__":
    main()
