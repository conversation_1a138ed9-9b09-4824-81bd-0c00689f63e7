"""
问题19：列出2021年所有发货时效超过5天的订单，包含订单号、商品名称和发货时效列（列出最大的五个）
"""

from data_utils import create_processor

def solve_question_19():
    """解决问题19：列出2021年所有发货时效超过5天的订单，包含订单号、商品名称和发货时效列（列出最大的五个）"""

    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()

    # 计算发货时效
    df = processor.calculate_delivery_time(df)

    # 过滤掉无效的发货时效数据
    df = df.dropna(subset=['发货时效(天)'])
    df = df[df['发货时效(天)'] >= 0]  # 过滤掉负值

    # 筛选发货时效超过5天的订单
    delayed_orders = df[df['发货时效(天)'] > 5]

    if len(delayed_orders) == 0:
        print("**没有找到发货时效超过5天的订单**")
        return

    # 按发货时效降序排序，取前5个
    top_delayed = delayed_orders.nlargest(5, '发货时效(天)')

    # 选择需要的列
    result_columns = ['订单号', '商品名称', '发货时效(天)', '销售金额', '数量', '学校', '仓库名称', '付款时间', '发货时间']
    result_df = top_delayed[result_columns].copy()
    result_df['发货时效(天)'] = result_df['发货时效(天)'].round(2)
    result_df['销售金额'] = result_df['销售金额'].round(2)

    # 输出结果
    processor.format_output(result_df, "发货时效超过5天的订单（前5个最长时效）")

    # 统计信息
    total_delayed = len(delayed_orders)
    total_orders = len(df)
    delayed_ratio = (total_delayed / total_orders * 100) if total_orders > 0 else 0

    print(f"\n**统计信息：**")
    print(f"- 发货时效超过5天的订单总数：{total_delayed} 个")
    print(f"- 占总订单比例：{delayed_ratio:.2f}%")
    print(f"- 最长发货时效：{delayed_orders['发货时效(天)'].max():.2f} 天")
    print(f"- 超过5天订单的平均时效：{delayed_orders['发货时效(天)'].mean():.2f} 天")

    # 按仓库统计超时订单
    warehouse_delayed = delayed_orders.groupby('仓库名称').agg({
        '订单号': 'count',
        '发货时效(天)': 'mean'
    }).reset_index()
    warehouse_delayed.columns = ['仓库名称', '超时订单数', '平均时效(天)']
    warehouse_delayed['平均时效(天)'] = warehouse_delayed['平均时效(天)'].round(2)
    warehouse_delayed = warehouse_delayed.sort_values('超时订单数', ascending=False)

    processor.format_output(warehouse_delayed, "各仓库超时订单统计")

    # 时效分布分析
    import pandas as pd
    time_ranges = [
        (5, 7, "5-7天"),
        (7, 10, "7-10天"),
        (10, 15, "10-15天"),
        (15, 30, "15-30天"),
        (30, float('inf'), "30天以上")
    ]

    distribution_data = []
    for min_days, max_days, label in time_ranges:
        if max_days == float('inf'):
            count = len(delayed_orders[delayed_orders['发货时效(天)'] >= min_days])
        else:
            count = len(delayed_orders[(delayed_orders['发货时效(天)'] >= min_days) &
                                     (delayed_orders['发货时效(天)'] < max_days)])
        percentage = (count / total_delayed * 100) if total_delayed > 0 else 0
        distribution_data.append({
            '时效范围': label,
            '订单数': count,
            '占比(%)': round(percentage, 2)
        })

    distribution_df = pd.DataFrame(distribution_data)
    processor.format_output(distribution_df, "超时订单时效分布")

if __name__ == "__main__":
    solve_question_19()
