"""
问题33：列出各仓库2021年春秋季针织下装的销售金额？
"""

from data_utils import create_processor
import pandas as pd

def solve_question_33():
    """解决问题33：列出各仓库2021年春秋季针织下装的销售金额？"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
# 各仓库春秋季针织下装销售金额
    spring_autumn_data = df[df['季节'] == '春秋']
    knit_pants_data = spring_autumn_data[spring_autumn_data['小类'] == '针织下装']
    
    warehouse_stats = knit_pants_data.groupby('仓库名称').agg({
        '销售金额': 'sum',
        '数量': 'sum',
        '订单号': 'count'
    }).reset_index()
    
    warehouse_stats.columns = ['仓库名称', '销售金额', '销售数量', '订单数']
    warehouse_stats['销售金额'] = warehouse_stats['销售金额'].round(2)
    warehouse_stats = warehouse_stats.sort_values('销售金额', ascending=False)
    
    processor.format_output(warehouse_stats, "各仓库春秋季针织下装销售金额")

if __name__ == "__main__":
    solve_question_33()
