"""
问题33：列出20 中冬季商品在武汉仓的销售数量及金额
"""

from data_utils import create_processor

def solve_question_33():
    """解决问题33：列出20 中冬季商品在武汉仓的销售数量及金额"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
    # TODO: 根据具体问题实现相应的数据分析逻辑
    print("问题33的实现待完成")
    print("问题描述：列出20 中冬季商品在武汉仓的销售数量及金额")
    
    # 基础数据统计
    print(f"\n**基础统计：**")
    print(f"- 总数据量：{len(df)} 条")
    print(f"- 学校数量：{df['学校'].nunique()} 个")
    print(f"- 小类数量：{df['小类'].nunique()} 个")
    print(f"- SKU数量：{df['SKU'].nunique()} 个")
    print(f"- 总销售金额：{df['销售金额'].sum():.2f} 元")
    print(f"- 总发货数量：{df['数量'].sum()} 件")

if __name__ == "__main__":
    solve_question_33()
