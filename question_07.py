"""
问题7：2021年所有校服中，哪个学校的校服退货率（基于订单状态统计）最高？具体数值是多少？
"""

from data_utils import create_processor

def solve_question_07():
    """解决问题7：退货率最高的学校"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
    # 计算各学校的退货率
    school_return_rate = processor.calculate_return_rate(df, '学校')
    
    # 输出结果
    processor.format_output(school_return_rate, "各学校退货率统计")
    
    # 输出退货率最高的学校信息
    if len(school_return_rate) > 0:
        top_school = school_return_rate.iloc[0]
        print(f"\n**答案：{top_school['学校']} 的校服退货率最高，具体数值是 {top_school['退货率(%)']}%**")
        
        # 详细分析
        print(f"\n**详细分析：**")
        print(f"- 该学校总订单数：{top_school['总订单数']} 个")
        print(f"- 问题订单数：{top_school['问题订单数']} 个")
        print(f"- 退货率：{top_school['退货率(%)']}%")
        
        # 显示订单状态分布
        import pandas as pd
        
        # 整体订单状态统计
        overall_status = df['系统订单状态'].value_counts()
        print(f"\n**整体订单状态分布：**")
        for status, count in overall_status.items():
            percentage = count / len(df) * 100
            print(f"- {status}: {count} 个订单 ({percentage:.1f}%)")
        
        # 退货率最高学校的订单状态分布
        top_school_data = df[df['学校'] == top_school['学校']]
        school_status = top_school_data['系统订单状态'].value_counts()
        print(f"\n**{top_school['学校']} 订单状态分布：**")
        for status, count in school_status.items():
            percentage = count / len(top_school_data) * 100
            print(f"- {status}: {count} 个订单 ({percentage:.1f}%)")
        
        # 退货率分析
        overall_avg_return = school_return_rate['退货率(%)'].mean()
        print(f"\n**退货率分析：**")
        print(f"- 整体平均退货率：{overall_avg_return:.2f}%")
        print(f"- 最高退货率学校：{top_school['学校']} ({top_school['退货率(%)']}%)")
        print(f"- 最低退货率学校：{school_return_rate.iloc[-1]['学校']} ({school_return_rate.iloc[-1]['退货率(%)']}%)")
        
        # 退货率分布
        return_rate_ranges = pd.cut(school_return_rate['退货率(%)'], 
                                   bins=[0, 5, 10, 20, 50, 100], 
                                   labels=['0-5%', '5-10%', '10-20%', '20-50%', '50-100%'])
        return_distribution = return_rate_ranges.value_counts().sort_index()
        
        print(f"\n**退货率分布：**")
        for range_name, count in return_distribution.items():
            if pd.notna(range_name):
                percentage = count / len(school_return_rate) * 100
                print(f"- {range_name}: {count} 个学校 ({percentage:.1f}%)")
                
    else:
        print("无法计算退货率，可能是数据中没有相关状态信息")

if __name__ == "__main__":
    solve_question_07()
