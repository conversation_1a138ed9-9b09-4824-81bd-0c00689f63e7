"""
检查问题实现状态
"""

import os

def check_implementation_status():
    """检查所有问题文件的实现状态"""
    
    implemented = []
    todo = []
    
    for i in range(9, 50):  # question_09.py 到 question_49.py
        file_name = f"question_{i:02d}.py"
        if os.path.exists(file_name):
            # 检查文件是否已实现（不包含TODO）
            with open(file_name, 'r', encoding='utf-8') as f:
                content = f.read()
                if "TODO" not in content and "待完成" not in content:
                    implemented.append(i)
                else:
                    todo.append(i)
        else:
            print(f"文件不存在: {file_name}")
    
    print(f"已完全实现的问题 ({len(implemented)}个): {implemented}")
    print(f"需要实现的问题 ({len(todo)}个): {todo}")
    
    return implemented, todo

if __name__ == "__main__":
    check_implementation_status()
