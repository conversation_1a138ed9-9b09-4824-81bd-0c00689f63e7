"""
问题32：列出20中2021年Q2的订单中，平均每单购买数量及金额？
"""

from data_utils import create_processor
import pandas as pd

def solve_question_32():
    """解决问题32：列出20中2021年Q2的订单中，平均每单购买数量及金额？"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
# 20中Q2订单平均每单购买数量及金额
    q2_data = processor.filter_by_quarter(df, '付款时间', 2021, 2)
    school_20_data = q2_data[q2_data['学校'] == '20中']
    
    if len(school_20_data) == 0:
        print("**没有找到20中2021年Q2的数据**")
        return
    
    avg_quantity = school_20_data['数量'].mean()
    avg_amount = school_20_data['销售金额'].mean()
    total_orders = len(school_20_data)
    
    result = pd.DataFrame({
        '学校': ['20中'],
        '时间': ['2021年Q2'],
        '总订单数': [total_orders],
        '平均每单购买数量': [round(avg_quantity, 2)],
        '平均每单金额': [round(avg_amount, 2)],
        '总销售数量': [school_20_data['数量'].sum()],
        '总销售金额': [round(school_20_data['销售金额'].sum(), 2)]
    })
    
    processor.format_output(result, "20中2021年Q2订单统计")
    print(f"\n**答案：20中2021年Q2平均每单购买数量 {avg_quantity:.2f} 件，平均每单金额 {avg_amount:.2f} 元**")

if __name__ == "__main__":
    solve_question_32()
