"""
问题22：列出2021年所有发货时效超过3天的仓库，包含仓库名称和平均发货时效
"""

from data_utils import create_processor

def solve_question_22():
    """解决问题22：列出2021年所有发货时效超过3天的仓库，包含仓库名称和平均发货时效"""

    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()

    # 计算发货时效
    df = processor.calculate_delivery_time(df)

    # 过滤掉无效的发货时效数据
    df = df.dropna(subset=['发货时效(天)'])
    df = df[df['发货时效(天)'] >= 0]  # 过滤掉负值

    # 按仓库统计平均发货时效
    warehouse_delivery = df.groupby('仓库名称')['发货时效(天)'].agg(['mean', 'count', 'std']).reset_index()
    warehouse_delivery.columns = ['仓库名称', '平均发货时效(天)', '订单数量', '时效标准差']
    warehouse_delivery['平均发货时效(天)'] = warehouse_delivery['平均发货时效(天)'].round(2)
    warehouse_delivery['时效标准差'] = warehouse_delivery['时效标准差'].round(2)

    # 筛选平均发货时效超过3天的仓库
    slow_warehouses = warehouse_delivery[warehouse_delivery['平均发货时效(天)'] > 3]

    # 按平均发货时效降序排序
    slow_warehouses = slow_warehouses.sort_values('平均发货时效(天)', ascending=False)

    # 输出结果
    processor.format_output(slow_warehouses, "发货时效超过3天的仓库")

    # 统计信息
    total_warehouses = len(warehouse_delivery)
    slow_count = len(slow_warehouses)

    print(f"\n**统计信息：**")
    print(f"- 总仓库数：{total_warehouses} 个")
    print(f"- 发货时效超过3天的仓库数：{slow_count} 个")
    print(f"- 占比：{slow_count/total_warehouses*100:.1f}%")

    if slow_count > 0:
        print(f"- 最慢仓库：{slow_warehouses.iloc[0]['仓库名称']} ({slow_warehouses.iloc[0]['平均发货时效(天)']} 天)")
        print(f"- 最快的慢仓库：{slow_warehouses.iloc[-1]['仓库名称']} ({slow_warehouses.iloc[-1]['平均发货时效(天)']} 天)")

    # 额外统计信息 - 显示各仓库的详细信息
    import pandas as pd
    stats_data = []
    for _, row in slow_warehouses.iterrows():
        warehouse = row['仓库名称']
        warehouse_data = df[df['仓库名称'] == warehouse]
        stats_data.append({
            '仓库名称': warehouse,
            '平均发货时效(天)': row['平均发货时效(天)'],
            '订单数量': row['订单数量'],
            '时效标准差': row['时效标准差'],
            '最短时效(天)': warehouse_data['发货时效(天)'].min(),
            '最长时效(天)': warehouse_data['发货时效(天)'].max(),
            '销售金额': warehouse_data['销售金额'].sum().round(2),
            '发货数量': warehouse_data['数量'].sum(),
            '学校数量': warehouse_data['学校'].nunique()
        })

    if stats_data:
        detailed_stats = pd.DataFrame(stats_data)
        processor.format_output(detailed_stats, "超时仓库详细统计信息")

    # 时效分布分析
    print(f"\n**整体时效分布分析：**")
    print(f"- 整体平均时效：{df['发货时效(天)'].mean():.2f} 天")
    print(f"- 时效中位数：{df['发货时效(天)'].median():.2f} 天")
    print(f"- 最短时效：{df['发货时效(天)'].min():.2f} 天")
    print(f"- 最长时效：{df['发货时效(天)'].max():.2f} 天")

if __name__ == "__main__":
    solve_question_22()
