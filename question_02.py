"""
问题2：列出2021年所有发货量超过1000件的校服小类，包含小类名称和发货量
"""

from data_utils import create_processor

def solve_question_02():
    """解决问题2：发货量超过1000件的校服小类"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
    # 筛选发货量超过1000件的小类
    high_volume_categories = processor.filter_by_threshold(
        df, 
        group_by='小类', 
        agg_column='数量', 
        threshold=1000,
        agg_func='sum'
    )
    
    # 重命名列
    high_volume_categories.columns = ['小类', '发货量']
    
    # 输出结果
    processor.format_output(high_volume_categories, "发货量超过1000件的校服小类")
    
    # 统计信息
    total_categories = len(high_volume_categories)
    total_volume = high_volume_categories['发货量'].sum()
    
    print(f"\n**统计信息：**")
    print(f"- 发货量超过1000件的小类数量：{total_categories} 个")
    print(f"- 这些小类的总发货量：{total_volume} 件")
    
    if total_categories > 0:
        avg_volume = high_volume_categories['发货量'].mean()
        max_volume = high_volume_categories['发货量'].max()
        print(f"- 平均发货量：{avg_volume:.0f} 件")
        print(f"- 最高发货量：{max_volume} 件")
        
        # 显示详细信息
        import pandas as pd
        stats_data = []
        for _, row in high_volume_categories.iterrows():
            category = row['小类']
            category_data = df[df['小类'] == category]
            stats_data.append({
                '小类': category,
                '发货量': row['发货量'],
                '销售金额': category_data['销售金额'].sum().round(2),
                '订单数量': len(category_data),
                '平均单价': category_data['折后单价'].mean().round(2),
                '主要学校': category_data['学校'].mode().iloc[0] if len(category_data['学校'].mode()) > 0 else 'N/A',
                '主要季节': category_data['季节'].mode().iloc[0] if len(category_data['季节'].mode()) > 0 else 'N/A'
            })
        
        detailed_stats = pd.DataFrame(stats_data)
        processor.format_output(detailed_stats, "高发货量小类详细统计信息")
    else:
        print("没有发货量超过1000件的校服小类")

if __name__ == "__main__":
    solve_question_02()
