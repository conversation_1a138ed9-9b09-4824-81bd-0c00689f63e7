"""
问题26：列出2021年所有退货率超过5%的校服小类，包含小类名称和退货率
"""

from data_utils import create_processor

def solve_question_26():
    """解决问题26：列出2021年所有退货率超过5%的校服小类，包含小类名称和退货率"""

    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()

    # 计算各小类的退货率
    category_return_rate = processor.calculate_return_rate_by_quantity(df, '小类')

    # 筛选有足够样本量的小类（至少50件）
    category_return_rate = category_return_rate[category_return_rate['总发货件数'] >= 50]

    # 筛选退货率超过5%的小类
    high_return_categories = category_return_rate[category_return_rate['退货率(%)'] > 5]

    # 按退货率降序排序
    high_return_categories = high_return_categories.sort_values('退货率(%)', ascending=False)

    # 输出结果
    processor.format_output(high_return_categories, "退货率超过5%的校服小类（最少50件样本）")

    # 统计信息
    total_categories = len(category_return_rate)
    high_return_count = len(high_return_categories)

    print(f"\n**统计信息：**")
    print(f"- 参与统计的小类总数：{total_categories} 个")
    print(f"- 退货率超过5%的小类数：{high_return_count} 个")
    print(f"- 占比：{high_return_count/total_categories*100:.1f}%")

    if high_return_count > 0:
        print(f"- 最高退货率小类：{high_return_categories.iloc[0]['小类']} ({high_return_categories.iloc[0]['退货率(%)']}%)")
        print(f"- 最低退货率（超过5%）：{high_return_categories.iloc[-1]['退货率(%)']}%")

    # 额外统计信息 - 显示各小类的详细信息
    import pandas as pd
    stats_data = []
    for _, row in high_return_categories.iterrows():
        category = row['小类']
        category_data = df[df['小类'] == category]
        stats_data.append({
            '小类': category,
            '退货率(%)': row['退货率(%)'],
            '总发货件数': row['总发货件数'],
            '退货件数': row['退货件数'],
            '销售金额': category_data['销售金额'].sum().round(2),
            '订单数量': len(category_data),
            '平均单价': category_data['折后单价'].mean().round(2),
            '学校数量': category_data['学校'].nunique(),
            '主要季节': category_data['季节'].mode().iloc[0] if len(category_data['季节'].mode()) > 0 else 'N/A',
            '主要学校': category_data['学校'].mode().iloc[0] if len(category_data['学校'].mode()) > 0 else 'N/A'
        })

    if stats_data:
        detailed_stats = pd.DataFrame(stats_data)
        processor.format_output(detailed_stats, "高退货率小类详细统计信息")

    # 退货率分布分析
    print(f"\n**退货率分布分析：**")
    rate_ranges = [
        (0, 5, "0-5%"),
        (5, 10, "5-10%"),
        (10, 20, "10-20%"),
        (20, 50, "20-50%"),
        (50, 100, "50%以上")
    ]

    for min_rate, max_rate, label in rate_ranges:
        count = len(category_return_rate[(category_return_rate['退货率(%)'] >= min_rate) &
                                       (category_return_rate['退货率(%)'] < max_rate)])
        percentage = (count / total_categories * 100) if total_categories > 0 else 0
        print(f"- {label}：{count} 个小类 ({percentage:.1f}%)")

if __name__ == "__main__":
    solve_question_26()
