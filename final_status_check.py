"""
最终状态检查脚本
"""

import os

def check_final_status():
    """检查所有问题文件的最终实现状态"""
    
    implemented = []
    todo = []
    missing = []
    
    for i in range(9, 50):  # question_09.py 到 question_49.py
        file_name = f"question_{i:02d}.py"
        if os.path.exists(file_name):
            # 检查文件是否已实现（不包含TODO）
            with open(file_name, 'r', encoding='utf-8') as f:
                content = f.read()
                if "TODO" not in content and "待完成" not in content:
                    implemented.append(i)
                else:
                    todo.append(i)
        else:
            missing.append(i)
    
    print("="*80)
    print("新增问题实现状态最终检查")
    print("="*80)
    
    print(f"\n✅ 已完全实现的问题 ({len(implemented)}个):")
    for i, q in enumerate(implemented):
        if i % 10 == 0 and i > 0:
            print()
        print(f"Q{q:02d}", end="  ")
    print()
    
    print(f"\n🔄 需要实现的问题 ({len(todo)}个):")
    for i, q in enumerate(todo):
        if i % 10 == 0 and i > 0:
            print()
        print(f"Q{q:02d}", end="  ")
    print()
    
    if missing:
        print(f"\n❌ 缺失的问题文件 ({len(missing)}个):")
        for i, q in enumerate(missing):
            if i % 10 == 0 and i > 0:
                print()
            print(f"Q{q:02d}", end="  ")
        print()
    
    total_questions = 41  # 从问题9到问题49，共41个问题
    completion_rate = len(implemented) / total_questions * 100
    
    print(f"\n📊 完成度统计:")
    print(f"- 总问题数: {total_questions}")
    print(f"- 已实现: {len(implemented)}")
    print(f"- 待实现: {len(todo)}")
    print(f"- 缺失文件: {len(missing)}")
    print(f"- 完成率: {completion_rate:.1f}%")
    
    # 显示进度条
    progress_bar_length = 50
    filled_length = int(progress_bar_length * completion_rate / 100)
    bar = '█' * filled_length + '░' * (progress_bar_length - filled_length)
    print(f"- 进度条: |{bar}| {completion_rate:.1f}%")
    
    return implemented, todo, missing

def show_question_mapping():
    """显示问题编号与描述的对应关系"""
    
    # 从新增问题.md读取问题描述
    questions_file = "新增问题.md"
    if not os.path.exists(questions_file):
        print("未找到新增问题.md文件")
        return
    
    print(f"\n📋 问题编号与描述对应关系:")
    print("-" * 80)
    
    with open(questions_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    question_num = 9  # 从问题9开始
    for line in lines[1:]:  # 跳过表头
        line = line.strip()
        if line and not line.startswith('|---'):
            # 提取问题描述
            if line.startswith('|') and line.endswith('|'):
                description = line[1:-1].strip()
                if description and question_num <= 49:
                    print(f"Q{question_num:02d}: {description}")
                    question_num += 1

if __name__ == "__main__":
    implemented, todo, missing = check_final_status()
    show_question_mapping()
