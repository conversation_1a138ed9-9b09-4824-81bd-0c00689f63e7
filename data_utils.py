"""
Kappa校服数据分析项目通用工具模块
提供数据读取、预处理、关联和输出格式化功能
"""

import pandas as pd
import re
from datetime import datetime

class KappaDataProcessor:
    """Kappa校服数据处理类，提供统一的数据操作接口"""
    
    def __init__(self, orders_file="21年校服线上销售订单.xlsx", category_file="校服小类信息.xlsx", sample_size=None):
        """
        初始化数据处理器

        Args:
            orders_file: 订单详细信息文件路径
            category_file: 校服小类信息文件路径
            sample_size: 限制读取的订单数据行数（用于测试，None表示读取全部）
        """
        self.orders_file = orders_file
        self.category_file = category_file
        self.sample_size = sample_size
        self.orders_df = None
        self.category_df = None
        self.merged_df = None
        
    def load_data(self):
        """加载原始数据"""
        try:
            print("正在加载数据...")
            if self.sample_size:
                print(f"使用样本模式，限制读取 {self.sample_size} 行订单数据")
                self.orders_df = pd.read_excel(self.orders_file, nrows=self.sample_size)
            else:
                self.orders_df = pd.read_excel(self.orders_file)
            self.category_df = pd.read_excel(self.category_file)
            print(f"成功加载数据：订单表 {len(self.orders_df)} 行，小类表 {len(self.category_df)} 行")
        except Exception as e:
            print(f"数据加载失败：{e}")
            raise
    
    def extract_size_from_spec(self, spec_name):
        """从规格名称中提取尺码"""
        if pd.isna(spec_name):
            return ""
        # 提取最后的数字部分作为尺码
        match = re.search(r'(\d+)$', str(spec_name))
        return match.group(1) if match else ""
    
    def extract_sku_from_code(self, product_code):
        """从商品编码中提取SKU部分"""
        if pd.isna(product_code):
            return ""
        # 去掉最后的尺码部分，保留SKU
        parts = str(product_code).rsplit('-', 1)
        return parts[0] if len(parts) > 1 else product_code
    
    def preprocess_data(self):
        """数据预处理和关联"""
        if self.orders_df is None or self.category_df is None:
            self.load_data()
        
        print("正在进行数据预处理...")
        
        # 处理订单数据
        orders_processed = self.orders_df.copy()
        
        # 提取尺码和SKU
        orders_processed['尺码'] = orders_processed['规格名称'].apply(self.extract_size_from_spec)
        orders_processed['SKU'] = orders_processed['商品编码'].apply(self.extract_sku_from_code)
        
        # 处理日期
        date_columns = ['发货时间', '完成时间', '付款时间']
        for col in date_columns:
            if col in orders_processed.columns:
                orders_processed[col] = pd.to_datetime(orders_processed[col], errors='coerce')
        
        # 关联小类信息
        self.merged_df = pd.merge(
            orders_processed, 
            self.category_df, 
            on='SKU', 
            how='left'
        )
        
        # 计算折扣率
        self.merged_df['折扣率'] = (self.merged_df['折后单价'] / self.merged_df['吊牌价']) * 100
        
        # 数据清理
        self.merged_df = self.merged_df.dropna(subset=['小类', '学校', '季节'])
        
        print(f"预处理完成：关联后数据 {len(self.merged_df)} 行")
        
        # 显示关联统计
        total_orders = len(orders_processed)
        matched_orders = len(self.merged_df)
        print(f"数据关联成功率: {matched_orders/total_orders*100:.1f}%")
        
    def get_merged_data(self):
        """获取合并后的数据"""
        if self.merged_df is None:
            self.preprocess_data()
        return self.merged_df.copy()
    
    def get_top_by_column(self, df, group_by, agg_column, agg_func='sum', top_n=10):
        """
        获取按指定列分组聚合后的前N名
        
        Args:
            df: 数据框
            group_by: 分组列
            agg_column: 聚合列
            agg_func: 聚合函数 ('sum', 'count', 'mean')
            top_n: 返回前N名
        """
        if agg_func == 'sum':
            result = df.groupby(group_by)[agg_column].sum().reset_index()
        elif agg_func == 'count':
            result = df.groupby(group_by)[agg_column].count().reset_index()
        elif agg_func == 'mean':
            result = df.groupby(group_by)[agg_column].mean().reset_index()
        else:
            raise ValueError("agg_func must be 'sum', 'count', or 'mean'")
        
        result = result.sort_values(agg_column, ascending=False)
        return result.head(top_n)
    
    def calculate_percentage(self, df, group_by, value_column):
        """计算占比"""
        grouped = df.groupby(group_by)[value_column].sum().reset_index()
        total = grouped[value_column].sum()
        grouped['占比(%)'] = (grouped[value_column] / total * 100).round(2)
        return grouped.sort_values('占比(%)', ascending=False)
    
    def filter_by_threshold(self, df, group_by, agg_column, threshold, agg_func='sum'):
        """按阈值筛选数据"""
        if agg_func == 'sum':
            grouped = df.groupby(group_by)[agg_column].sum().reset_index()
        elif agg_func == 'count':
            grouped = df.groupby(group_by)[agg_column].count().reset_index()
        else:
            raise ValueError("agg_func must be 'sum' or 'count'")
        
        return grouped[grouped[agg_column] > threshold].sort_values(agg_column, ascending=False)
    
    def calculate_return_rate(self, df, group_by):
        """计算退货率（基于订单状态）"""
        # 统计各状态的订单数量
        status_stats = df.groupby([group_by, '系统订单状态']).size().unstack(fill_value=0)
        
        # 计算总订单数和退货订单数
        total_orders = status_stats.sum(axis=1)
        
        # 假设非"已发货"状态为问题订单（包括退货、取消等）
        problem_orders = total_orders - status_stats.get('已发货', 0)
        
        # 计算退货率
        return_rate = (problem_orders / total_orders * 100).round(2)
        
        result = pd.DataFrame({
            group_by: return_rate.index,
            '总订单数': total_orders.values,
            '问题订单数': problem_orders.values,
            '退货率(%)': return_rate.values
        }).sort_values('退货率(%)', ascending=False)
        
        return result
    
    def format_output(self, df, title="分析结果"):
        """
        格式化输出为Markdown格式
        
        Args:
            df: 要输出的数据框
            title: 结果标题
        """
        print(f"\n## {title}")
        print(df.to_markdown(index=False))
        return df

# 便捷函数
def create_processor(sample_size=None):
    """创建并初始化数据处理器"""
    processor = KappaDataProcessor(sample_size=sample_size)
    processor.preprocess_data()
    return processor
