"""
问题39：列出20中冬季商品在武汉仓的销售数量及金额
"""

from data_utils import create_processor
import pandas as pd

def solve_question_39():
    """解决问题39：列出20中冬季商品在武汉仓的销售数量及金额"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
# 20中冬季商品在武汉仓的销售数量及金额
    winter_data = df[df['季节'] == '冬']
    school_20_data = winter_data[winter_data['学校'] == '20中']
    wuhan_data = school_20_data[school_20_data['仓库名称'] == '武汉仓']
    
    if len(wuhan_data) == 0:
        print("**没有找到20中冬季商品在武汉仓的数据**")
        return
    
    total_quantity = wuhan_data['数量'].sum()
    total_amount = wuhan_data['销售金额'].sum()
    
    result = pd.DataFrame({
        '学校': ['20中'],
        '季节': ['冬'],
        '仓库': ['武汉仓'],
        '销售数量': [total_quantity],
        '销售金额': [round(total_amount, 2)],
        '订单数': [len(wuhan_data)]
    })
    
    processor.format_output(result, "20中冬季商品在武汉仓销售统计")
    print(f"\n**答案：20中冬季商品在武汉仓销售数量 {total_quantity} 件，销售金额 {total_amount:.2f} 元**")

if __name__ == "__main__":
    solve_question_39()
