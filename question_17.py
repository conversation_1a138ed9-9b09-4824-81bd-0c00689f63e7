"""
问题17：2021年Q4，哪个仓库的发货时效（发货时间-付款时间）平均值最低？具体时效是多少？
"""

from data_utils import create_processor

def solve_question_17():
    """解决问题17：Q4发货时效平均值最低的仓库"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
    # 筛选2021年Q4数据
    q4_data = processor.filter_by_quarter(df, '付款时间', 2021, 4)
    
    # 计算发货时效
    q4_data = processor.calculate_delivery_time(q4_data)
    
    # 过滤掉无效的发货时效数据
    q4_data = q4_data.dropna(subset=['发货时效(天)'])
    q4_data = q4_data[q4_data['发货时效(天)'] >= 0]  # 过滤掉负值
    
    # 按仓库统计平均发货时效
    warehouse_delivery = q4_data.groupby('仓库名称')['发货时效(天)'].agg(['mean', 'count', 'std']).reset_index()
    warehouse_delivery.columns = ['仓库名称', '平均发货时效(天)', '订单数量', '时效标准差']
    warehouse_delivery['平均发货时效(天)'] = warehouse_delivery['平均发货时效(天)'].round(2)
    warehouse_delivery['时效标准差'] = warehouse_delivery['时效标准差'].round(2)
    
    # 按平均发货时效排序（从低到高）
    warehouse_delivery = warehouse_delivery.sort_values('平均发货时效(天)', ascending=True)
    
    # 输出结果
    processor.format_output(warehouse_delivery, "2021年Q4各仓库平均发货时效排名（从低到高）")
    
    # 输出时效最低的仓库信息
    best_warehouse = warehouse_delivery.iloc[0]
    print(f"\n**答案：{best_warehouse['仓库名称']} 仓库的发货时效平均值最低，具体时效是 {best_warehouse['平均发货时效(天)']} 天**")
    
    # 额外统计信息 - 显示各仓库的详细信息
    import pandas as pd
    stats_data = []
    for _, row in warehouse_delivery.iterrows():
        warehouse = row['仓库名称']
        warehouse_data = q4_data[q4_data['仓库名称'] == warehouse]
        stats_data.append({
            '仓库名称': warehouse,
            '平均发货时效(天)': row['平均发货时效(天)'],
            '订单数量': row['订单数量'],
            '时效标准差': row['时效标准差'],
            '最短时效(天)': warehouse_data['发货时效(天)'].min(),
            '最长时效(天)': warehouse_data['发货时效(天)'].max(),
            '销售金额': warehouse_data['销售金额'].sum().round(2),
            '发货数量': warehouse_data['数量'].sum(),
            '主要学校': warehouse_data['学校'].mode().iloc[0] if len(warehouse_data['学校'].mode()) > 0 else 'N/A'
        })
    
    detailed_stats = pd.DataFrame(stats_data)
    processor.format_output(detailed_stats, "2021年Q4各仓库详细统计信息")
    
    # 发货时效分析
    print(f"\n**发货时效分析：**")
    print(f"- Q4总订单数：{len(q4_data)} 个")
    print(f"- 参与统计仓库数：{len(warehouse_delivery)} 个")
    print(f"- 最快仓库：{best_warehouse['仓库名称']} ({best_warehouse['平均发货时效(天)']} 天)")
    print(f"- 最慢仓库：{warehouse_delivery.iloc[-1]['仓库名称']} ({warehouse_delivery.iloc[-1]['平均发货时效(天)']} 天)")
    print(f"- 整体平均时效：{q4_data['发货时效(天)'].mean():.2f} 天")
    
    # 时效分布分析
    print(f"\n**时效分布分析：**")
    time_ranges = [
        (0, 1, "1天内"),
        (1, 2, "1-2天"),
        (2, 3, "2-3天"),
        (3, 5, "3-5天"),
        (5, float('inf'), "5天以上")
    ]
    
    for min_days, max_days, label in time_ranges:
        if max_days == float('inf'):
            count = len(q4_data[q4_data['发货时效(天)'] >= min_days])
        else:
            count = len(q4_data[(q4_data['发货时效(天)'] >= min_days) & (q4_data['发货时效(天)'] < max_days)])
        percentage = (count / len(q4_data) * 100) if len(q4_data) > 0 else 0
        print(f"- {label}：{count} 个订单 ({percentage:.1f}%)")

if __name__ == "__main__":
    solve_question_17()
