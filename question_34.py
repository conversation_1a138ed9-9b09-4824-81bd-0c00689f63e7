"""
问题34：2021 年销售数量为 0 的款号及对应小类
"""

from data_utils import create_processor

def solve_question_34():
    """解决问题34：2021 年销售数量为 0 的款号及对应小类"""

    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()

    # 获取小类信息表中的所有款号
    category_df = processor.category_df
    all_styles = set(category_df['款号'].unique())

    # 获取订单数据中有销售记录的款号
    sold_styles = set(df['款号'].unique())

    # 找出销售数量为0的款号（在小类表中但不在订单表中）
    zero_sales_styles = all_styles - sold_styles

    if len(zero_sales_styles) == 0:
        print("**所有款号都有销售记录，没有销售数量为0的款号**")
        return

    # 获取这些款号的详细信息
    zero_sales_info = category_df[category_df['款号'].isin(zero_sales_styles)].copy()
    zero_sales_info = zero_sales_info[['款号', '小类', '学校', '季节', '吊牌价', '销售价']].drop_duplicates()
    zero_sales_info = zero_sales_info.sort_values(['小类', '款号'])

    # 输出结果
    processor.format_output(zero_sales_info, "2021年销售数量为0的款号及对应小类")

    # 统计信息
    print(f"\n**统计信息：**")
    print(f"- 小类表中总款号数：{len(all_styles)} 个")
    print(f"- 有销售记录的款号数：{len(sold_styles)} 个")
    print(f"- 销售数量为0的款号数：{len(zero_sales_styles)} 个")
    print(f"- 零销售款号占比：{len(zero_sales_styles)/len(all_styles)*100:.2f}%")

    # 按小类统计零销售款号
    category_zero_stats = zero_sales_info.groupby('小类').agg({
        '款号': 'count'
    }).reset_index()
    category_zero_stats.columns = ['小类', '零销售款号数']
    category_zero_stats = category_zero_stats.sort_values('零销售款号数', ascending=False)

    processor.format_output(category_zero_stats, "各小类零销售款号统计")

    # 按学校统计零销售款号
    school_zero_stats = zero_sales_info.groupby('学校').agg({
        '款号': 'count'
    }).reset_index()
    school_zero_stats.columns = ['学校', '零销售款号数']
    school_zero_stats = school_zero_stats.sort_values('零销售款号数', ascending=False)

    processor.format_output(school_zero_stats, "各学校零销售款号统计")

if __name__ == "__main__":
    solve_question_34()
