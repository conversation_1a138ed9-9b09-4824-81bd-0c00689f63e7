"""
问题49：列出武汉仓2021年湖州吴兴实验学校棉服的订单数及平均每单金额
"""

from data_utils import create_processor

def solve_question_49():
    """解决问题49：列出武汉仓2021年湖州吴兴实验学校棉服的订单数及平均每单金额"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
    # 筛选武汉仓、湖州吴兴实验学校、棉服的数据
    target_data = df[
        (df['仓库名称'] == '武汉仓') & 
        (df['学校'] == '湖州吴兴实验') & 
        (df['小类'] == '棉服')
    ]
    
    if len(target_data) == 0:
        print("**没有找到武汉仓湖州吴兴实验学校棉服的数据**")
        return
    
    # 统计订单数和平均每单金额
    order_count = len(target_data)
    total_amount = target_data['销售金额'].sum()
    avg_amount_per_order = total_amount / order_count if order_count > 0 else 0
    total_quantity = target_data['数量'].sum()
    
    # 创建结果汇总
    import pandas as pd
    summary_data = pd.DataFrame({
        '仓库': ['武汉仓'],
        '学校': ['湖州吴兴实验'],
        '小类': ['棉服'],
        '订单数': [order_count],
        '总销售金额': [round(total_amount, 2)],
        '平均每单金额': [round(avg_amount_per_order, 2)],
        '总销售数量': [total_quantity],
        '平均每单数量': [round(total_quantity/order_count, 1) if order_count > 0 else 0]
    })
    
    # 输出结果
    processor.format_output(summary_data, "武汉仓湖州吴兴实验学校棉服订单统计")
    
    print(f"\n**答案：武汉仓湖州吴兴实验学校棉服订单数为 {order_count} 个，平均每单金额为 {avg_amount_per_order:.2f} 元**")
    
    # 详细订单信息
    if len(target_data) > 0:
        order_details = target_data[['订单号', '款号', '销售金额', '数量', '折后单价', '付款时间']].copy()
        order_details['销售金额'] = order_details['销售金额'].round(2)
        order_details['折后单价'] = order_details['折后单价'].round(2)
        order_details = order_details.sort_values('销售金额', ascending=False)
        
        processor.format_output(order_details, "详细订单信息")
    
    # 按款号统计
    if len(target_data) > 0:
        style_stats = target_data.groupby('款号').agg({
            '销售金额': ['sum', 'count', 'mean'],
            '数量': 'sum'
        }).reset_index()
        
        style_stats.columns = ['款号', '总销售金额', '订单数', '平均每单金额', '销售数量']
        style_stats['总销售金额'] = style_stats['总销售金额'].round(2)
        style_stats['平均每单金额'] = style_stats['平均每单金额'].round(2)
        style_stats = style_stats.sort_values('总销售金额', ascending=False)
        
        processor.format_output(style_stats, "按款号统计")

if __name__ == "__main__":
    solve_question_49()
