"""
问题36：当前各学校中，订单客单价超过学校平均值的订单数及占比
"""

from data_utils import create_processor

def solve_question_36():
    """解决问题36：各学校订单客单价超过学校平均值的订单数及占比"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
    # 计算各学校的平均客单价
    school_avg_order_value = df.groupby('学校')['销售金额'].mean().reset_index()
    school_avg_order_value.columns = ['学校', '学校平均客单价']
    
    # 将平均客单价合并回原数据
    df_with_avg = df.merge(school_avg_order_value, on='学校', how='left')
    
    # 标记超过平均值的订单
    df_with_avg['超过平均值'] = df_with_avg['销售金额'] > df_with_avg['学校平均客单价']
    
    # 按学校统计超过平均值的订单数及占比
    school_stats = df_with_avg.groupby('学校').agg({
        '超过平均值': ['sum', 'count'],
        '销售金额': 'mean'
    }).reset_index()
    
    # 重新整理列名
    school_stats.columns = ['学校', '超过平均值订单数', '总订单数', '平均客单价']
    school_stats['占比(%)'] = (school_stats['超过平均值订单数'] / school_stats['总订单数'] * 100).round(2)
    school_stats['平均客单价'] = school_stats['平均客单价'].round(2)
    
    # 按超过平均值订单数排序
    school_stats = school_stats.sort_values('超过平均值订单数', ascending=False)
    
    # 输出结果
    processor.format_output(school_stats.head(20), "各学校订单客单价超过学校平均值统计（前20名）")
    
    # 额外统计信息 - 显示各学校的详细信息
    import pandas as pd
    stats_data = []
    for _, row in school_stats.head(15).iterrows():  # 只显示前15个的详细信息
        school = row['学校']
        school_data = df[df['学校'] == school]
        above_avg_data = df_with_avg[(df_with_avg['学校'] == school) & (df_with_avg['超过平均值'])]
        
        stats_data.append({
            '学校': school,
            '超过平均值订单数': row['超过平均值订单数'],
            '总订单数': row['总订单数'],
            '占比(%)': row['占比(%)'],
            '学校平均客单价': row['平均客单价'],
            '超平均值订单平均金额': above_avg_data['销售金额'].mean().round(2) if len(above_avg_data) > 0 else 0,
            '总销售金额': school_data['销售金额'].sum().round(2),
            '发货数量': school_data['数量'].sum(),
            '小类数量': school_data['小类'].nunique(),
            '主要季节': school_data['季节'].mode().iloc[0] if len(school_data['季节'].mode()) > 0 else 'N/A'
        })
    
    detailed_stats = pd.DataFrame(stats_data)
    processor.format_output(detailed_stats, "前15个学校详细统计信息")
    
    # 整体分析
    total_orders = len(df)
    total_above_avg = df_with_avg['超过平均值'].sum()
    overall_ratio = (total_above_avg / total_orders * 100) if total_orders > 0 else 0
    
    print(f"\n**整体分析：**")
    print(f"- 总学校数：{len(school_stats)} 个")
    print(f"- 总订单数：{total_orders} 个")
    print(f"- 超过学校平均值的订单总数：{total_above_avg} 个")
    print(f"- 整体占比：{overall_ratio:.2f}%")
    print(f"- 平均每校超平均值订单数：{school_stats['超过平均值订单数'].mean():.1f} 个")
    
    # 占比分布分析
    print(f"\n**占比分布分析：**")
    ratio_ranges = [
        (0, 30, "0-30%"),
        (30, 40, "30-40%"),
        (40, 50, "40-50%"),
        (50, 60, "50-60%"),
        (60, 100, "60%以上")
    ]
    
    for min_ratio, max_ratio, label in ratio_ranges:
        count = len(school_stats[(school_stats['占比(%)'] >= min_ratio) & (school_stats['占比(%)'] < max_ratio)])
        percentage = (count / len(school_stats) * 100) if len(school_stats) > 0 else 0
        print(f"- {label}：{count} 个学校 ({percentage:.1f}%)")
    
    # 最高和最低占比学校
    if len(school_stats) > 0:
        highest_ratio_school = school_stats.loc[school_stats['占比(%)'].idxmax()]
        lowest_ratio_school = school_stats.loc[school_stats['占比(%)'].idxmin()]
        
        print(f"\n**极值分析：**")
        print(f"- 最高占比学校：{highest_ratio_school['学校']} ({highest_ratio_school['占比(%)']:.2f}%)")
        print(f"- 最低占比学校：{lowest_ratio_school['学校']} ({lowest_ratio_school['占比(%)']:.2f}%)")

if __name__ == "__main__":
    solve_question_36()
