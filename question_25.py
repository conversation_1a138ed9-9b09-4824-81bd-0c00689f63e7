"""
问题25：2021年所有校服中，哪个SKU的平均折扣率（折后单价/吊牌价）最低？具体数值是多少？
"""

from data_utils import create_processor

def solve_question_25():
    """解决问题25：2021年所有校服中，哪个SKU的平均折扣率（折后单价/吊牌价）最低？具体数值是多少？"""

    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()

    # 按SKU统计平均折扣率
    sku_discount = df.groupby('SKU')['折扣率'].agg(['mean', 'count', 'std']).reset_index()
    sku_discount.columns = ['SKU', '平均折扣率(%)', '订单数量', '折扣率标准差']
    sku_discount['平均折扣率(%)'] = sku_discount['平均折扣率(%)'].round(2)
    sku_discount['折扣率标准差'] = sku_discount['折扣率标准差'].round(2)

    # 筛选有足够样本量的SKU（至少5个订单）
    sku_discount = sku_discount[sku_discount['订单数量'] >= 5]

    # 按平均折扣率升序排序（最低的在前）
    sku_discount = sku_discount.sort_values('平均折扣率(%)', ascending=True)

    # 输出结果
    processor.format_output(sku_discount.head(15), "各SKU平均折扣率排名（从低到高，前15名，最少5个订单）")

    # 输出折扣率最低的SKU信息
    if len(sku_discount) > 0:
        lowest_sku = sku_discount.iloc[0]
        print(f"\n**答案：SKU {lowest_sku['SKU']} 的平均折扣率最低，具体数值是 {lowest_sku['平均折扣率(%)']}%**")

        # 获取该SKU的详细信息
        sku_data = df[df['SKU'] == lowest_sku['SKU']]
        if len(sku_data) > 0:
            print(f"- 商品名称：{sku_data['商品名称'].iloc[0]}")
            print(f"- 所属学校：{sku_data['学校'].iloc[0]}")
            print(f"- 小类：{sku_data['小类'].iloc[0]}")
            print(f"- 季节：{sku_data['季节'].iloc[0]}")
            print(f"- 平均吊牌价：{sku_data['吊牌价'].mean():.2f} 元")
            print(f"- 平均折后单价：{sku_data['折后单价'].mean():.2f} 元")
            print(f"- 订单数量：{lowest_sku['订单数量']} 个")
    else:
        print("\n**没有找到符合条件的SKU（至少5个订单）**")
        return

    # 额外统计信息 - 显示各SKU的详细信息
    import pandas as pd
    stats_data = []
    for _, row in sku_discount.head(10).iterrows():  # 只显示前10个的详细信息
        sku = row['SKU']
        sku_data = df[df['SKU'] == sku]
        stats_data.append({
            'SKU': sku,
            '平均折扣率(%)': row['平均折扣率(%)'],
            '订单数量': row['订单数量'],
            '折扣率标准差': row['折扣率标准差'],
            '平均吊牌价': sku_data['吊牌价'].mean().round(2),
            '平均折后单价': sku_data['折后单价'].mean().round(2),
            '销售金额': sku_data['销售金额'].sum().round(2),
            '发货数量': sku_data['数量'].sum(),
            '学校': sku_data['学校'].iloc[0] if len(sku_data) > 0 else 'N/A',
            '小类': sku_data['小类'].iloc[0] if len(sku_data) > 0 else 'N/A'
        })

    detailed_stats = pd.DataFrame(stats_data)
    processor.format_output(detailed_stats, "前10个SKU详细统计信息")

    # 折扣率分析
    print(f"\n**折扣率分析：**")
    print(f"- 参与统计SKU数：{len(sku_discount)} 个")
    print(f"- 最低折扣率：{sku_discount.iloc[0]['平均折扣率(%)']}%")
    print(f"- 最高折扣率：{sku_discount.iloc[-1]['平均折扣率(%)']}%")
    print(f"- 平均折扣率：{sku_discount['平均折扣率(%)'].mean():.2f}%")
    print(f"- 折扣率中位数：{sku_discount['平均折扣率(%)'].median():.2f}%")

if __name__ == "__main__":
    solve_question_25()
