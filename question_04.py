"""
问题4：2021年所有校服中，哪个学校的校服销售额最高？具体数值是多少？
"""

from data_utils import create_processor

def solve_question_04():
    """解决问题4：销售额最高的学校"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
    # 按学校统计销售金额
    school_sales = processor.get_top_by_column(
        df, 
        group_by='学校', 
        agg_column='销售金额', 
        agg_func='sum',
        top_n=15  # 显示前15个学校
    )
    
    # 重命名列
    school_sales.columns = ['学校', '销售金额']
    school_sales['销售金额'] = school_sales['销售金额'].round(2)
    
    # 输出结果
    processor.format_output(school_sales, "各学校销售金额排名")
    
    # 输出最高的学校信息
    top_school = school_sales.iloc[0]
    print(f"\n**答案：{top_school['学校']} 的校服销售额最高，具体数值是 {top_school['销售金额']:.2f} 元**")
    
    # 额外统计信息 - 显示各学校的详细信息
    import pandas as pd
    stats_data = []
    for _, row in school_sales.head(10).iterrows():  # 只显示前10个的详细信息
        school = row['学校']
        school_data = df[df['学校'] == school]
        stats_data.append({
            '学校': school,
            '销售金额': row['销售金额'],
            '订单数量': len(school_data),
            '发货数量': school_data['数量'].sum(),
            '平均单价': school_data['折后单价'].mean().round(2),
            '平均折扣率(%)': school_data['折扣率'].mean().round(2),
            '小类数量': school_data['小类'].nunique(),
            '主要季节': school_data['季节'].mode().iloc[0] if len(school_data['季节'].mode()) > 0 else 'N/A'
        })
    
    detailed_stats = pd.DataFrame(stats_data)
    processor.format_output(detailed_stats, "前10个学校详细统计信息")
    
    # 总体统计
    total_schools = len(school_sales)
    total_sales = school_sales['销售金额'].sum()
    avg_sales = school_sales['销售金额'].mean()
    
    print(f"\n**总体统计：**")
    print(f"- 学校总数：{total_schools} 个")
    print(f"- 总销售金额：{total_sales:.2f} 元")
    print(f"- 平均销售金额：{avg_sales:.2f} 元")

if __name__ == "__main__":
    solve_question_04()
