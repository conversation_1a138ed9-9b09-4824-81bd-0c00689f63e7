"""
问题15：2021年所有校服中，哪个学校的明细状态为成功的数量最高？具体数值是多少？
"""

from data_utils import create_processor

def solve_question_15():
    """解决问题15：明细状态为成功数量最高的学校"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
    # 计算各学校明细状态为成功的数量
    school_success = processor.calculate_success_count(df, '学校')
    
    # 输出结果
    processor.format_output(school_success.head(15), "各学校明细状态为成功的数量排名（前15名）")
    
    # 输出最高的学校信息
    top_school = school_success.iloc[0]
    print(f"\n**答案：{top_school['学校']} 的明细状态为成功的数量最高，具体数值是 {top_school['成功数量']} 件**")
    
    # 额外统计信息 - 显示各学校的详细信息
    import pandas as pd
    stats_data = []
    for _, row in school_success.head(10).iterrows():  # 只显示前10个的详细信息
        school = row['学校']
        school_data = df[df['学校'] == school]
        success_data = school_data[school_data['明细状态'] == '成功']
        
        # 计算成功率
        total_quantity = school_data['数量'].sum()
        success_rate = (row['成功数量'] / total_quantity * 100) if total_quantity > 0 else 0
        
        stats_data.append({
            '学校': school,
            '成功数量': row['成功数量'],
            '总发货量': total_quantity,
            '成功率(%)': round(success_rate, 2),
            '成功订单数': len(success_data),
            '总订单数': len(school_data),
            '销售金额': school_data['销售金额'].sum().round(2),
            '平均单价': school_data['折后单价'].mean().round(2),
            '小类数量': school_data['小类'].nunique()
        })
    
    detailed_stats = pd.DataFrame(stats_data)
    processor.format_output(detailed_stats, "前10个学校详细统计信息")
    
    # 成功率分析
    print(f"\n**成功率分析：**")
    print(f"- 最高成功数量学校：{top_school['学校']} ({top_school['成功数量']} 件)")
    
    # 统计各种明细状态
    status_stats = df.groupby(['学校', '明细状态'])['数量'].sum().unstack(fill_value=0)
    print(f"\n**明细状态分布统计：**")
    print(f"- 总学校数：{len(school_success)} 个")
    print(f"- 明细状态类型：{df['明细状态'].unique()}")

if __name__ == "__main__":
    solve_question_15()
