"""
问题5：2021年所有校服中，哪个季节的校服平均折扣率（折后单价/吊牌价）最低？具体数值是多少？
"""

from data_utils import create_processor

def solve_question_05():
    """解决问题5：平均折扣率最低的季节"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
    # 按季节统计平均折扣率
    season_discount = processor.get_top_by_column(
        df, 
        group_by='季节', 
        agg_column='折扣率', 
        agg_func='mean',
        top_n=10  # 显示所有季节
    )
    
    # 重命名列并按折扣率升序排列（最低的在前）
    season_discount.columns = ['季节', '平均折扣率(%)']
    season_discount['平均折扣率(%)'] = season_discount['平均折扣率(%)'].round(2)
    season_discount = season_discount.sort_values('平均折扣率(%)', ascending=True)
    
    # 输出结果
    processor.format_output(season_discount, "各季节平均折扣率排名（从低到高）")
    
    # 输出最低的季节信息
    lowest_season = season_discount.iloc[0]
    print(f"\n**答案：{lowest_season['季节']} 季节的校服平均折扣率最低，具体数值是 {lowest_season['平均折扣率(%)']:.2f}%**")
    
    # 额外统计信息 - 显示各季节的详细信息
    import pandas as pd
    stats_data = []
    for _, row in season_discount.iterrows():
        season = row['季节']
        season_data = df[df['季节'] == season]
        stats_data.append({
            '季节': season,
            '平均折扣率(%)': row['平均折扣率(%)'],
            '订单数量': len(season_data),
            '销售金额': season_data['销售金额'].sum().round(2),
            '发货数量': season_data['数量'].sum(),
            '平均吊牌价': season_data['吊牌价'].mean().round(2),
            '平均折后单价': season_data['折后单价'].mean().round(2),
            '小类数量': season_data['小类'].nunique(),
            '学校数量': season_data['学校'].nunique()
        })
    
    detailed_stats = pd.DataFrame(stats_data)
    processor.format_output(detailed_stats, "各季节详细统计信息")
    
    # 折扣率分析
    overall_avg_discount = df['折扣率'].mean()
    print(f"\n**折扣率分析：**")
    print(f"- 整体平均折扣率：{overall_avg_discount:.2f}%")
    print(f"- 最低折扣率季节：{lowest_season['季节']} ({lowest_season['平均折扣率(%)']:.2f}%)")
    print(f"- 最高折扣率季节：{season_discount.iloc[-1]['季节']} ({season_discount.iloc[-1]['平均折扣率(%)']:.2f}%)")
    
    # 折扣率分布
    discount_ranges = pd.cut(df['折扣率'], bins=[0, 30, 50, 70, 90, 100], labels=['0-30%', '30-50%', '50-70%', '70-90%', '90-100%'])
    discount_distribution = discount_ranges.value_counts().sort_index()
    
    print(f"\n**折扣率分布：**")
    for range_name, count in discount_distribution.items():
        percentage = count / len(df) * 100
        print(f"- {range_name}: {count} 个订单 ({percentage:.1f}%)")

if __name__ == "__main__":
    solve_question_05()
