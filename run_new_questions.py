"""
运行所有新增问题的脚本
"""

import os
import sys
import importlib
import traceback
from datetime import datetime

def run_question(question_num):
    """运行单个问题"""
    module_name = f"question_{question_num:02d}"
    function_name = f"solve_question_{question_num:02d}"
    
    try:
        # 动态导入模块
        module = importlib.import_module(module_name)
        
        # 获取解决函数
        solve_function = getattr(module, function_name)
        
        print(f"\n{'='*80}")
        print(f"运行问题 {question_num:02d}")
        print(f"{'='*80}")
        
        # 运行函数
        solve_function()
        
        print(f"\n问题 {question_num:02d} 运行完成")
        return True
        
    except ImportError as e:
        print(f"问题 {question_num:02d}: 模块导入失败 - {e}")
        return False
    except AttributeError as e:
        print(f"问题 {question_num:02d}: 函数不存在 - {e}")
        return False
    except Exception as e:
        print(f"问题 {question_num:02d}: 运行出错 - {e}")
        print(f"错误详情：\n{traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print(f"开始运行新增问题分析")
    print(f"运行时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查已实现的问题文件
    implemented_questions = []
    for i in range(9, 50):  # question_09.py 到 question_49.py
        file_name = f"question_{i:02d}.py"
        if os.path.exists(file_name):
            # 检查文件是否已实现（不包含TODO）
            with open(file_name, 'r', encoding='utf-8') as f:
                content = f.read()
                if "TODO" not in content and "待完成" not in content:
                    implemented_questions.append(i)
    
    print(f"发现已实现的问题：{implemented_questions}")
    
    # 运行已实现的问题
    success_count = 0
    failed_count = 0
    
    for question_num in implemented_questions:
        success = run_question(question_num)
        if success:
            success_count += 1
        else:
            failed_count += 1
    
    # 总结
    print(f"\n{'='*80}")
    print(f"运行总结")
    print(f"{'='*80}")
    print(f"- 总问题数：{len(implemented_questions)}")
    print(f"- 成功运行：{success_count}")
    print(f"- 运行失败：{failed_count}")
    print(f"- 成功率：{success_count/(success_count+failed_count)*100:.1f}%" if (success_count+failed_count) > 0 else "0%")
    
    # 列出未实现的问题
    all_questions = list(range(9, 50))
    unimplemented = [q for q in all_questions if q not in implemented_questions]
    if unimplemented:
        print(f"\n未实现的问题：{unimplemented}")
        print(f"未实现问题数：{len(unimplemented)}")

if __name__ == "__main__":
    main()
