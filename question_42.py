"""
问题42：列出湖州吴兴实验学校中，各款号的销售数量
"""

from data_utils import create_processor

def solve_question_42():
    """解决问题42：列出湖州吴兴实验学校中，各款号的销售数量"""

    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()

    # 筛选湖州吴兴实验学校的数据
    school_data = df[df['学校'] == '湖州吴兴实验']

    if len(school_data) == 0:
        print("**没有找到湖州吴兴实验学校的数据**")
        print(f"可用学校：{list(df['学校'].unique())}")
        return

    # 按款号统计销售数量
    style_stats = school_data.groupby('款号').agg({
        '数量': 'sum',
        '销售金额': 'sum',
        '订单号': 'count',
        '小类': 'first',
        '季节': 'first',
        '折后单价': 'mean'
    }).reset_index()

    style_stats.columns = ['款号', '销售数量', '销售金额', '订单数', '小类', '季节', '平均单价']
    style_stats['销售金额'] = style_stats['销售金额'].round(2)
    style_stats['平均单价'] = style_stats['平均单价'].round(2)
    style_stats = style_stats.sort_values('销售数量', ascending=False)

    # 输出结果
    processor.format_output(style_stats, "湖州吴兴实验学校各款号销售数量")

    # 统计分析
    total_quantity = style_stats['销售数量'].sum()
    total_amount = style_stats['销售金额'].sum()
    total_orders = style_stats['订单数'].sum()

    print(f"\n**湖州吴兴实验学校销售汇总：**")
    print(f"- 总销售数量：{total_quantity} 件")
    print(f"- 总销售金额：{total_amount:.2f} 元")
    print(f"- 总订单数：{total_orders} 个")
    print(f"- 款号数量：{len(style_stats)} 个")
    print(f"- 涉及小类：{school_data['小类'].nunique()} 个")
    print(f"- 涉及季节：{school_data['季节'].nunique()} 个")

    # 按小类汇总
    category_summary = school_data.groupby('小类')['数量'].sum().reset_index()
    category_summary.columns = ['小类', '销售数量']
    category_summary = category_summary.sort_values('销售数量', ascending=False)

    processor.format_output(category_summary, "按小类汇总")

    # 按季节汇总
    season_summary = school_data.groupby('季节')['数量'].sum().reset_index()
    season_summary.columns = ['季节', '销售数量']
    season_summary = season_summary.sort_values('销售数量', ascending=False)

    processor.format_output(season_summary, "按季节汇总")

if __name__ == "__main__":
    solve_question_42()
