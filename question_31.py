"""
问题31：海淀进修学校春秋季节的销售数量及金额汇总
"""

from data_utils import create_processor

def solve_question_31():
    """解决问题31：海淀进修学校春秋季节的销售数量及金额汇总"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
    # 筛选海淀进修学校春秋季节的数据
    school_season_data = df[(df['学校'] == '海淀进修') & (df['季节'] == '春秋')]

    if len(school_season_data) == 0:
        print("**没有找到海淀进修学校春秋季节的数据**")
        return
    
    # 汇总统计
    total_quantity = school_season_data['数量'].sum()
    total_amount = school_season_data['销售金额'].sum()
    order_count = len(school_season_data)
    
    # 创建汇总结果
    import pandas as pd
    summary_data = pd.DataFrame({
        '学校': ['海淀进修'],
        '季节': ['春秋'],
        '销售数量': [total_quantity],
        '销售金额': [round(total_amount, 2)],
        '订单数量': [order_count],
        '平均单价': [round(school_season_data['折后单价'].mean(), 2)],
        '平均折扣率(%)': [round(school_season_data['折扣率'].mean(), 2)]
    })
    
    # 输出汇总结果
    processor.format_output(summary_data, "海淀进修学校春秋季节销售汇总")
    
    # 按小类详细统计
    category_stats = school_season_data.groupby('小类').agg({
        '数量': 'sum',
        '销售金额': 'sum',
        '订单号': 'count',
        '折后单价': 'mean',
        '折扣率': 'mean'
    }).reset_index()
    
    category_stats.columns = ['小类', '销售数量', '销售金额', '订单数量', '平均单价', '平均折扣率(%)']
    category_stats['销售金额'] = category_stats['销售金额'].round(2)
    category_stats['平均单价'] = category_stats['平均单价'].round(2)
    category_stats['平均折扣率(%)'] = category_stats['平均折扣率(%)'].round(2)
    category_stats = category_stats.sort_values('销售金额', ascending=False)
    
    processor.format_output(category_stats, "海淀进修学校春秋季节各小类详细统计")
    
    # 按款号详细统计（前10名）
    style_stats = school_season_data.groupby('款号').agg({
        '数量': 'sum',
        '销售金额': 'sum',
        '订单号': 'count',
        '小类': 'first',
        '折后单价': 'mean'
    }).reset_index()
    
    style_stats.columns = ['款号', '销售数量', '销售金额', '订单数量', '小类', '平均单价']
    style_stats['销售金额'] = style_stats['销售金额'].round(2)
    style_stats['平均单价'] = style_stats['平均单价'].round(2)
    style_stats = style_stats.sort_values('销售金额', ascending=False)
    
    processor.format_output(style_stats.head(10), "海淀进修学校春秋季节各款号详细统计（前10名）")
    
    # 时间分布分析
    school_season_data['月份'] = school_season_data['付款时间'].dt.month
    monthly_stats = school_season_data.groupby('月份').agg({
        '数量': 'sum',
        '销售金额': 'sum',
        '订单号': 'count'
    }).reset_index()
    
    monthly_stats.columns = ['月份', '销售数量', '销售金额', '订单数量']
    monthly_stats['销售金额'] = monthly_stats['销售金额'].round(2)
    
    processor.format_output(monthly_stats, "海淀进修学校春秋季节月度销售分布")
    
    # 详细分析
    print(f"\n**详细分析：**")
    print(f"- 总销售数量：{total_quantity} 件")
    print(f"- 总销售金额：{total_amount:.2f} 元")
    print(f"- 总订单数量：{order_count} 个")
    print(f"- 平均每单数量：{total_quantity/order_count:.1f} 件")
    print(f"- 平均每单金额：{total_amount/order_count:.2f} 元")
    print(f"- 涉及小类数量：{school_season_data['小类'].nunique()} 个")
    print(f"- 涉及款号数量：{school_season_data['款号'].nunique()} 个")

if __name__ == "__main__":
    solve_question_31()
