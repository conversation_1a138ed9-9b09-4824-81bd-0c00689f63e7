"""
问题35：2021年1月第一周，武汉仓发货时效超过48小时的订单占比及平均延误时长
"""

from data_utils import create_processor

def solve_question_35():
    """解决问题35：1月第一周武汉仓发货时效超过48小时的订单占比及平均延误时长"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
    # 筛选2021年1月第一周武汉仓的数据
    week1_data = processor.filter_by_week(df, '付款时间', 2021, 1, 1)
    wuhan_week1_data = week1_data[week1_data['仓库名称'] == '武汉仓']
    
    if len(wuhan_week1_data) == 0:
        print("**没有找到2021年1月第一周武汉仓的数据**")
        return
    
    # 计算发货时效（小时）
    wuhan_week1_data = processor.calculate_delivery_time(wuhan_week1_data)
    wuhan_week1_data['发货时效(小时)'] = wuhan_week1_data['发货时效(天)'] * 24
    
    # 过滤掉无效的发货时效数据
    wuhan_week1_data = wuhan_week1_data.dropna(subset=['发货时效(小时)'])
    wuhan_week1_data = wuhan_week1_data[wuhan_week1_data['发货时效(小时)'] >= 0]
    
    if len(wuhan_week1_data) == 0:
        print("**没有找到有效的发货时效数据**")
        return
    
    # 统计超过48小时的订单
    total_orders = len(wuhan_week1_data)
    delayed_orders = wuhan_week1_data[wuhan_week1_data['发货时效(小时)'] > 48]
    delayed_count = len(delayed_orders)
    delayed_ratio = (delayed_count / total_orders * 100) if total_orders > 0 else 0
    
    # 计算平均延误时长（仅针对超过48小时的订单）
    if delayed_count > 0:
        avg_delay = delayed_orders['发货时效(小时)'].mean()
        avg_excess_delay = avg_delay - 48  # 超出48小时的部分
    else:
        avg_delay = 0
        avg_excess_delay = 0
    
    # 创建结果汇总
    import pandas as pd
    summary_data = pd.DataFrame({
        '时间范围': ['2021年1月第一周'],
        '仓库': ['武汉仓'],
        '总订单数': [total_orders],
        '超时订单数': [delayed_count],
        '超时占比(%)': [round(delayed_ratio, 2)],
        '平均延误时长(小时)': [round(avg_delay, 2)],
        '超出48小时部分(小时)': [round(avg_excess_delay, 2)]
    })
    
    # 输出结果
    processor.format_output(summary_data, "武汉仓1月第一周发货时效分析")
    
    print(f"\n**答案：**")
    print(f"- 超过48小时的订单占比：{delayed_ratio:.2f}%")
    print(f"- 平均延误时长：{avg_delay:.2f} 小时")
    print(f"- 超出48小时的平均延误：{avg_excess_delay:.2f} 小时")
    
    # 详细时效分布分析
    time_ranges = [
        (0, 24, "24小时内"),
        (24, 48, "24-48小时"),
        (48, 72, "48-72小时"),
        (72, 120, "72-120小时"),
        (120, float('inf'), "120小时以上")
    ]
    
    distribution_data = []
    for min_hours, max_hours, label in time_ranges:
        if max_hours == float('inf'):
            count = len(wuhan_week1_data[wuhan_week1_data['发货时效(小时)'] >= min_hours])
        else:
            count = len(wuhan_week1_data[(wuhan_week1_data['发货时效(小时)'] >= min_hours) & 
                                       (wuhan_week1_data['发货时效(小时)'] < max_hours)])
        percentage = (count / total_orders * 100) if total_orders > 0 else 0
        distribution_data.append({
            '时效范围': label,
            '订单数': count,
            '占比(%)': round(percentage, 2)
        })
    
    distribution_df = pd.DataFrame(distribution_data)
    processor.format_output(distribution_df, "发货时效分布详情")
    
    # 超时订单详细信息（前10个）
    if delayed_count > 0:
        delayed_details = delayed_orders[['订单号', '商品名称', '发货时效(小时)', '销售金额', '数量']].copy()
        delayed_details['发货时效(小时)'] = delayed_details['发货时效(小时)'].round(2)
        delayed_details['销售金额'] = delayed_details['销售金额'].round(2)
        delayed_details = delayed_details.sort_values('发货时效(小时)', ascending=False)
        
        processor.format_output(delayed_details.head(10), "超时订单详情（前10个，按时效降序）")
    
    # 统计分析
    print(f"\n**统计分析：**")
    print(f"- 分析时间：2021年1月第一周")
    print(f"- 分析仓库：武汉仓")
    print(f"- 总订单数：{total_orders} 个")
    print(f"- 48小时内完成：{total_orders - delayed_count} 个 ({100 - delayed_ratio:.2f}%)")
    print(f"- 超过48小时：{delayed_count} 个 ({delayed_ratio:.2f}%)")
    print(f"- 整体平均时效：{wuhan_week1_data['发货时效(小时)'].mean():.2f} 小时")
    print(f"- 最快时效：{wuhan_week1_data['发货时效(小时)'].min():.2f} 小时")
    print(f"- 最慢时效：{wuhan_week1_data['发货时效(小时)'].max():.2f} 小时")

if __name__ == "__main__":
    solve_question_35()
