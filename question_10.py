"""
问题10：列出2021年所有发货量超过1000件的前10个校服小类，包含小类名称和发货量
"""

from data_utils import create_processor

def solve_question_10():
    """解决问题10：发货量超过1000件的前10个校服小类"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
    # 按小类统计发货量
    category_quantity = processor.get_top_by_column(
        df, 
        group_by='小类', 
        agg_column='数量', 
        agg_func='sum',
        top_n=50  # 先获取更多数据用于筛选
    )
    
    # 重命名列
    category_quantity.columns = ['小类', '发货量']
    
    # 筛选发货量超过1000件的小类
    filtered_categories = category_quantity[category_quantity['发货量'] > 1000]
    
    # 取前10个
    top_10_categories = filtered_categories.head(10)
    
    # 输出结果
    processor.format_output(top_10_categories, "发货量超过1000件的前10个校服小类")
    
    # 统计信息
    total_qualified = len(filtered_categories)
    print(f"\n**统计信息：**")
    print(f"- 发货量超过1000件的小类总数：{total_qualified} 个")
    print(f"- 显示前10个小类")
    
    if len(top_10_categories) > 0:
        print(f"- 最高发货量：{top_10_categories.iloc[0]['发货量']} 件（{top_10_categories.iloc[0]['小类']}）")
        print(f"- 第10名发货量：{top_10_categories.iloc[-1]['发货量']} 件（{top_10_categories.iloc[-1]['小类']}）")
    
    # 额外统计信息 - 显示各小类的详细信息
    import pandas as pd
    stats_data = []
    for _, row in top_10_categories.iterrows():
        category = row['小类']
        category_data = df[df['小类'] == category]
        stats_data.append({
            '小类': category,
            '发货量': row['发货量'],
            '销售金额': category_data['销售金额'].sum().round(2),
            '订单数量': len(category_data),
            '平均单价': category_data['折后单价'].mean().round(2),
            '主要季节': category_data['季节'].mode().iloc[0] if len(category_data['季节'].mode()) > 0 else 'N/A',
            '主要学校': category_data['学校'].mode().iloc[0] if len(category_data['学校'].mode()) > 0 else 'N/A'
        })
    
    detailed_stats = pd.DataFrame(stats_data)
    processor.format_output(detailed_stats, "前10个小类详细统计信息")

if __name__ == "__main__":
    solve_question_10()
