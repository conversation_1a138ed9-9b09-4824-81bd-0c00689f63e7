"""
问题38：列出武汉仓2021年各小类的销售数量
"""

from data_utils import create_processor

def solve_question_38():
    """解决问题38：列出武汉仓2021年各小类的销售数量"""

    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()

    # 筛选武汉仓的数据
    wuhan_data = df[df['仓库名称'] == '武汉仓']

    if len(wuhan_data) == 0:
        print("**没有找到武汉仓的数据**")
        print(f"可用仓库：{list(df['仓库名称'].unique())}")
        return

    # 按小类统计销售数量
    category_stats = wuhan_data.groupby('小类').agg({
        '数量': 'sum',
        '销售金额': 'sum',
        '订单号': 'count',
        '折后单价': 'mean'
    }).reset_index()

    category_stats.columns = ['小类', '销售数量', '销售金额', '订单数', '平均单价']
    category_stats['销售金额'] = category_stats['销售金额'].round(2)
    category_stats['平均单价'] = category_stats['平均单价'].round(2)
    category_stats = category_stats.sort_values('销售数量', ascending=False)

    # 输出结果
    processor.format_output(category_stats, "武汉仓2021年各小类销售数量")

    # 统计信息
    total_quantity = category_stats['销售数量'].sum()
    total_amount = category_stats['销售金额'].sum()
    total_orders = category_stats['订单数'].sum()

    print(f"\n**武汉仓2021年销售汇总：**")
    print(f"- 总销售数量：{total_quantity} 件")
    print(f"- 总销售金额：{total_amount:.2f} 元")
    print(f"- 总订单数：{total_orders} 个")
    print(f"- 小类数量：{len(category_stats)} 个")
    print(f"- 平均每单数量：{total_quantity/total_orders:.1f} 件")
    print(f"- 平均每单金额：{total_amount/total_orders:.2f} 元")

    # 按季节统计
    season_stats = wuhan_data.groupby('季节').agg({
        '数量': 'sum',
        '销售金额': 'sum',
        '订单号': 'count'
    }).reset_index()

    season_stats.columns = ['季节', '销售数量', '销售金额', '订单数']
    season_stats['销售金额'] = season_stats['销售金额'].round(2)
    season_stats = season_stats.sort_values('销售数量', ascending=False)

    processor.format_output(season_stats, "武汉仓各季节销售统计")

    # 按学校统计（前10名）
    school_stats = wuhan_data.groupby('学校').agg({
        '数量': 'sum',
        '销售金额': 'sum',
        '订单号': 'count'
    }).reset_index()

    school_stats.columns = ['学校', '销售数量', '销售金额', '订单数']
    school_stats['销售金额'] = school_stats['销售金额'].round(2)
    school_stats = school_stats.sort_values('销售数量', ascending=False)

    processor.format_output(school_stats.head(10), "武汉仓各学校销售统计（前10名）")

    # 销售占比分析
    print(f"\n**销售占比分析：**")
    print(f"- 销售量最高小类：{category_stats.iloc[0]['小类']} ({category_stats.iloc[0]['销售数量']} 件)")
    print(f"- 前3个小类销售量占比：{category_stats.head(3)['销售数量'].sum()/total_quantity*100:.1f}%")
    print(f"- 前5个小类销售量占比：{category_stats.head(5)['销售数量'].sum()/total_quantity*100:.1f}%")

if __name__ == "__main__":
    solve_question_38()
