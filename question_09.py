"""
问题9：2021年销售额最高的校服小类是什么？具体销售额是多少？
"""

from data_utils import create_processor

def solve_question_09():
    """解决问题9：销售额最高的校服小类"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
    # 按小类统计销售金额
    category_sales = processor.get_top_by_column(
        df, 
        group_by='小类', 
        agg_column='销售金额', 
        agg_func='sum',
        top_n=15  # 显示前15个小类
    )
    
    # 重命名列
    category_sales.columns = ['小类', '销售金额']
    category_sales['销售金额'] = category_sales['销售金额'].round(2)
    
    # 输出结果
    processor.format_output(category_sales, "各小类销售金额排名")
    
    # 输出最高的小类信息
    top_category = category_sales.iloc[0]
    print(f"\n**答案：{top_category['小类']} 小类的销售额最高，具体销售额是 {top_category['销售金额']:.2f} 元**")
    
    # 额外统计信息 - 显示各小类的详细信息
    import pandas as pd
    stats_data = []
    for _, row in category_sales.head(10).iterrows():  # 只显示前10个的详细信息
        category = row['小类']
        category_data = df[df['小类'] == category]
        stats_data.append({
            '小类': category,
            '销售金额': row['销售金额'],
            '订单数量': len(category_data),
            '发货数量': category_data['数量'].sum(),
            '平均单价': category_data['折后单价'].mean().round(2),
            '平均折扣率(%)': category_data['折扣率'].mean().round(2),
            '主要季节': category_data['季节'].mode().iloc[0] if len(category_data['季节'].mode()) > 0 else 'N/A'
        })
    
    detailed_stats = pd.DataFrame(stats_data)
    processor.format_output(detailed_stats, "前10个小类详细统计信息")

if __name__ == "__main__":
    solve_question_09()
