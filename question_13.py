"""
问题13：2021年所有校服中，哪个季节的校服平均折扣率（折后单价/吊牌价）最低？具体数值是多少？
"""

from data_utils import create_processor

def solve_question_13():
    """解决问题13：平均折扣率最低的季节"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
    # 按季节统计平均折扣率
    season_discount = processor.get_top_by_column(
        df, 
        group_by='季节', 
        agg_column='折扣率', 
        agg_func='mean',
        top_n=10
    )
    
    # 重命名列并排序（折扣率从低到高）
    season_discount.columns = ['季节', '平均折扣率(%)']
    season_discount['平均折扣率(%)'] = season_discount['平均折扣率(%)'].round(2)
    season_discount = season_discount.sort_values('平均折扣率(%)', ascending=True)
    
    # 输出结果
    processor.format_output(season_discount, "各季节平均折扣率排名（从低到高）")
    
    # 输出最低折扣率的季节信息
    lowest_season = season_discount.iloc[0]
    print(f"\n**答案：{lowest_season['季节']} 季节的校服平均折扣率最低，具体数值是 {lowest_season['平均折扣率(%)']:.2f}%**")
    
    # 额外统计信息 - 显示各季节的详细信息
    import pandas as pd
    stats_data = []
    for _, row in season_discount.iterrows():
        season = row['季节']
        season_data = df[df['季节'] == season]
        stats_data.append({
            '季节': season,
            '平均折扣率(%)': row['平均折扣率(%)'],
            '销售金额': season_data['销售金额'].sum().round(2),
            '订单数量': len(season_data),
            '发货数量': season_data['数量'].sum(),
            '平均单价': season_data['折后单价'].mean().round(2),
            '平均吊牌价': season_data['吊牌价'].mean().round(2),
            '小类数量': season_data['小类'].nunique(),
            '学校数量': season_data['学校'].nunique()
        })
    
    detailed_stats = pd.DataFrame(stats_data)
    processor.format_output(detailed_stats, "各季节详细统计信息")
    
    # 折扣率分析
    print(f"\n**折扣率分析：**")
    for _, row in season_discount.iterrows():
        season = row['季节']
        discount_rate = row['平均折扣率(%)']
        print(f"- {season}：{discount_rate}%（折扣力度：{100-discount_rate:.2f}%）")
    
    # 折扣率分布可视化（文字版）
    print(f"\n**折扣率分布：**")
    for _, row in season_discount.iterrows():
        season = row['季节']
        discount_rate = row['平均折扣率(%)']
        bar_length = int(discount_rate / 5)  # 每5%一个字符
        bar = '█' * bar_length
        print(f"- {season:6s}: {bar} {discount_rate}%")

if __name__ == "__main__":
    solve_question_13()
