# Kappa校服数据分析项目

## 项目概述

本项目基于2021年Kappa校服销售数据，通过Python和pandas进行数据分析，回答8个关键业务问题。

## 数据集说明

### 数据文件
1. **21年校服线上销售订单.xlsx** - 订单详细信息表（26个字段）
2. **校服小类信息.xlsx** - 商品分类信息表（7个字段）

### 数据关联
- **关联方式**：通过SKU与商品编码进行关联
- **关联公式**：商品编码 = SKU + 规格名称中的尺码数字
- **关联成功率**：约99.8%

## 项目结构

```
2023年kappa销售数据/
├── data_utils.py          # 数据处理工具模块
├── question_01.py         # 问题1：销售额最高的校服小类
├── question_02.py         # 问题2：发货量超过1000件的校服小类
├── question_03.py         # 问题3：各季节校服销售额占比
├── question_04.py         # 问题4：销售额最高的学校
├── question_05.py         # 问题5：平均折扣率最低的季节
├── question_06.py         # 问题6：发货量超过500件的学校
├── question_07.py         # 问题7：退货率最高的学校
├── question_08.py         # 问题8：销售额占比最高的季节
├── 要求.md               # 项目需求文档
└── README.md             # 项目说明文档
```

## 核心功能

### data_utils.py 工具模块
- **KappaDataProcessor类**：统一的数据处理接口
- **数据加载**：支持Excel文件读取和样本模式
- **数据预处理**：自动关联两个数据表
- **分析功能**：提供排序、筛选、占比计算等功能
- **输出格式化**：统一的Markdown格式输出

### 主要分析维度
- **小类分析**：按校服小类统计销售额和发货量
- **季节分析**：按季节分析销售占比和折扣率
- **学校分析**：按学校统计销售额、发货量和退货率
- **折扣率分析**：计算各维度的折扣率统计

## 使用方法

### 运行单个分析问题
```bash
python question_01.py  # 运行问题1
python question_02.py  # 运行问题2
# ... 以此类推
```

### 使用样本模式（快速测试）
修改各问题文件中的create_processor调用：
```python
processor = create_processor(sample_size=5000)  # 限制5000行
```

### 使用完整数据
```python
processor = create_processor()  # 使用全部数据
```

## 分析结果示例（基于5000行样本）

### 问题1：销售额最高的校服小类
**答案**：针织下装小类的销售额最高，具体销售额是227,430.00元

### 问题2：发货量超过1000件的校服小类
**结果**：
- 针织下装：1,737件
- 短袖POLO：1,612件

### 问题3：各季节校服销售额占比
**结果**：
- 夏季：61.84%
- 春秋：36.87%
- 冬季：1.29%

### 问题4：销售额最高的学校
**答案**：海淀进修的校服销售额最高，具体数值是295,175.00元

### 问题5：平均折扣率最低的季节
**答案**：春秋季节的校服平均折扣率最低，具体数值是26.98%

## 技术特点

1. **模块化设计**：通用工具模块，避免代码重复
2. **准确的数据关联**：基于SKU和尺码的精确关联逻辑
3. **灵活的数据处理**：支持样本模式和完整数据模式
4. **标准化输出**：统一的Markdown格式，便于阅读和报告
5. **详细的统计信息**：每个分析都包含丰富的统计细节

## 数据质量

- **数据关联成功率**：99.8%
- **数据完整性**：自动处理缺失值和异常数据
- **数据一致性**：统一的数据类型和格式处理

## 扩展性

项目设计支持：
- 添加新的分析问题
- 扩展数据源
- 自定义分析维度
- 修改输出格式

## 注意事项

1. 确保Excel文件在正确的路径下
2. 大数据文件加载可能需要较长时间
3. 建议先使用样本模式进行测试
4. 所有结果基于2021年的数据
