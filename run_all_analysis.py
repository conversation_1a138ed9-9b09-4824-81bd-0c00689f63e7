"""
运行所有分析问题的脚本
"""

import sys
import time
from datetime import datetime

def run_analysis():
    """运行所有8个分析问题"""
    
    print("=" * 80)
    print("Kappa校服数据分析项目 - 完整分析报告")
    print("=" * 80)
    print(f"分析时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 分析问题列表
    questions = [
        ("question_01.py", "问题1：销售额最高的校服小类"),
        ("question_02.py", "问题2：发货量超过1000件的校服小类"),
        ("question_03.py", "问题3：各季节校服销售额占比"),
        ("question_04.py", "问题4：销售额最高的学校"),
        ("question_05.py", "问题5：平均折扣率最低的季节"),
        ("question_06.py", "问题6：发货量超过500件的学校"),
        ("question_07.py", "问题7：退货率最高的学校"),
        ("question_08.py", "问题8：销售额占比最高的季节")
    ]
    
    total_start_time = time.time()
    
    for i, (script, description) in enumerate(questions, 1):
        print(f"\n{'='*60}")
        print(f"正在执行 {description}")
        print(f"{'='*60}")
        
        start_time = time.time()
        
        try:
            # 动态导入并执行
            module_name = script.replace('.py', '')
            module = __import__(module_name)
            
            # 执行主函数
            if hasattr(module, f'solve_question_{i:02d}'):
                getattr(module, f'solve_question_{i:02d}')()
            else:
                print(f"警告：未找到函数 solve_question_{i:02d}")
                
        except Exception as e:
            print(f"执行 {script} 时出错：{e}")
            continue
        
        end_time = time.time()
        print(f"\n执行时间：{end_time - start_time:.2f} 秒")
    
    total_end_time = time.time()
    
    print(f"\n{'='*80}")
    print("分析完成！")
    print(f"总执行时间：{total_end_time - total_start_time:.2f} 秒")
    print(f"完成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

if __name__ == "__main__":
    # 检查是否使用样本模式
    if len(sys.argv) > 1 and sys.argv[1] == "--sample":
        print("使用样本模式运行分析（限制5000行数据）")
        # 修改data_utils中的默认样本大小
        import data_utils
        original_create = data_utils.create_processor
        data_utils.create_processor = lambda: original_create(sample_size=5000)
    
    run_analysis()
