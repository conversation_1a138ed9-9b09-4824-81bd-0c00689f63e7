"""
批量生成剩余问题文件的脚本
"""

import os

# 问题列表（从新增问题.md中提取）
questions = [
    "2021年销售额最高的校服小类是什么？具体销售额是多少？",
    "列出2021年所有发货量超过1000件的前10个校服小类，包含小类名称和发货量",
    "2021年各季节校服的销售额占比分别是多少？",
    "2021年所有校服中，哪个学校的校服销售额最高？具体数值是多少？",
    "2021年所有校服中，哪个季节的校服平均折扣率（折后单价/吊牌价）最低？具体数值是多少？",
    "列出2021年所有发货量超过500件的学校，包含学校名称和发货量",
    "2021年所有校服中，哪个学校的明细状态为成功的数量最高？具体数值是多少？",
    "2021年所有校服中，哪个季节的校服销售额占比最高？具体数值是多少？",
    "2021年Q4，哪个仓库的发货时效（发货时间-付款时间）平均值最低？具体时效是多少？",
    "2021年所有校服中，哪个SKU的退货率（退货件数/总销售件数）最高？具体数值是多少？",
    "列出2021年所有发货时效超过5天的订单，包含订单号、商品名称和发货时效列（列出最大的五个）",
    "2021年所有校服中，哪个仓库的退货率（退货件数/总发货件数）最低？具体数值是多少？",
    "2021年所有校服中，哪个SKU的销售额超过平均值的订单数量最多？具体数量是多少？",
    "列出2021年所有发货时效超过3天的仓库，包含仓库名称和平均发货时效",
    "2021年所有校服中，哪个SKU的销售额占比最高？具体数值是多少？",
    "列出2021年所有发货量超过1000件的仓库，包含仓库名称和发货量",
    "2021年所有校服中，哪个SKU的平均折扣率（折后单价/吊牌价）最低？具体数值是多少？",
    "列出2021年所有退货率超过5%的校服小类，包含小类名称和退货率",
    "2021年2月所有校服中，哪个SKU的销售额超过平均值的订单数量最多？具体数量是多少？",
    "2021年所有校服中，哪个仓库的发货时效（发货时间-付款时间）平均值最低？具体时效是多少？",
    "2021年春秋季校服的销售金额总计是多少？其中销售金额超过平均值的订单有多少个？",
    "列出2021年各仓库中销售金额最高的校服款号及其销售金额。",
    "海淀进修学校春秋季节的销售数量及金额汇总",
    "列出学校为海淀进修学校，且销售数量TOP5的款号？",
    "列出20 中冬季商品在武汉仓的销售数量及金额",
    "2021 年销售数量为 0 的款号及对应小类",
    "2021年1月第一周，武汉仓发货时效超过 48 小时的订单占比及平均延误时长",
    "当前各学校中，订单客单价超过学校平均值的订单数及占比",
    "列出徐水仓2021年Q1棉服的销售数量及订单数",
    "列出武汉仓2021年各小类的销售数量",
    "列出20中2021年Q2的订单中，平均每单购买数量及金额？",
    "列出各仓库2021年春秋季针织下装的销售金额？",
    "列出海淀进修学校2021年10月的订单发货时效，发货时效为发货时间 - 付款时间。",
    "列出湖州吴兴实验学校中，各款号的销售数量",
    "列出各仓库2021年春秋20中的销售金额占比",
    "列出各学校2021年Q3羽绒马甲的销售数量？",
    "海淀进修学校2021年11月各小类的销售金额",
    "列出湖州吴兴实验学校2021年6月棉服的销售金额及发货时效",
    "列出徐水仓2021年海淀进修学校针织开衫的销售数量及金额",
    "列出武汉仓2021年湖州吴兴实验学校棉服的订单数及平均每单金额"
]

def generate_question_file(question_num, question_text):
    """生成单个问题文件"""
    
    # 根据问题内容生成相应的代码逻辑
    function_name = f"solve_question_{question_num:02d}"
    file_name = f"question_{question_num:02d}.py"
    
    # 基础模板
    template = f'''"""
问题{question_num}：{question_text}
"""

from data_utils import create_processor

def {function_name}():
    """解决问题{question_num}：{question_text}"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
    # TODO: 根据具体问题实现相应的数据分析逻辑
    print("问题{question_num}的实现待完成")
    print("问题描述：{question_text}")
    
    # 基础数据统计
    print(f"\\n**基础统计：**")
    print(f"- 总数据量：{{len(df)}} 条")
    print(f"- 学校数量：{{df['学校'].nunique()}} 个")
    print(f"- 小类数量：{{df['小类'].nunique()}} 个")
    print(f"- SKU数量：{{df['SKU'].nunique()}} 个")
    print(f"- 总销售金额：{{df['销售金额'].sum():.2f}} 元")
    print(f"- 总发货数量：{{df['数量'].sum()}} 件")

if __name__ == "__main__":
    {function_name}()
'''
    
    return template

def main():
    """主函数：批量生成问题文件"""
    
    # 检查已存在的文件
    existing_files = []
    for i in range(9, 50):  # question_09.py 到 question_49.py
        file_name = f"question_{i:02d}.py"
        if os.path.exists(file_name):
            existing_files.append(file_name)
    
    print(f"已存在的文件：{existing_files}")
    
    # 生成缺失的文件
    generated_files = []
    for i, question_text in enumerate(questions, 9):  # 从问题9开始
        file_name = f"question_{i:02d}.py"
        
        if not os.path.exists(file_name):
            content = generate_question_file(i, question_text)
            
            with open(file_name, 'w', encoding='utf-8') as f:
                f.write(content)
            
            generated_files.append(file_name)
            print(f"生成文件：{file_name}")
    
    print(f"\\n总共生成了 {len(generated_files)} 个文件")
    print("生成的文件需要根据具体问题手动实现相应的分析逻辑")

if __name__ == "__main__":
    main()
