"""
问题6：列出2021年所有发货量超过500件的学校校服，包含学校名称和发货量
"""

from data_utils import create_processor

def solve_question_06():
    """解决问题6：发货量超过500件的学校"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
    # 筛选发货量超过500件的学校
    high_volume_schools = processor.filter_by_threshold(
        df, 
        group_by='学校', 
        agg_column='数量', 
        threshold=500,
        agg_func='sum'
    )
    
    # 重命名列
    high_volume_schools.columns = ['学校', '发货量']
    
    # 输出结果
    processor.format_output(high_volume_schools, "发货量超过500件的学校")
    
    # 统计信息
    total_schools = len(high_volume_schools)
    total_volume = high_volume_schools['发货量'].sum()
    
    print(f"\n**统计信息：**")
    print(f"- 发货量超过500件的学校数量：{total_schools} 个")
    print(f"- 这些学校的总发货量：{total_volume} 件")
    
    if total_schools > 0:
        avg_volume = high_volume_schools['发货量'].mean()
        max_volume = high_volume_schools['发货量'].max()
        min_volume = high_volume_schools['发货量'].min()
        print(f"- 平均发货量：{avg_volume:.0f} 件")
        print(f"- 最高发货量：{max_volume} 件")
        print(f"- 最低发货量：{min_volume} 件")
        
        # 显示详细信息
        import pandas as pd
        stats_data = []
        for _, row in high_volume_schools.iterrows():
            school = row['学校']
            school_data = df[df['学校'] == school]
            stats_data.append({
                '学校': school,
                '发货量': row['发货量'],
                '销售金额': school_data['销售金额'].sum().round(2),
                '订单数量': len(school_data),
                '平均单价': school_data['折后单价'].mean().round(2),
                '平均折扣率(%)': school_data['折扣率'].mean().round(2),
                '小类数量': school_data['小类'].nunique(),
                '主要季节': school_data['季节'].mode().iloc[0] if len(school_data['季节'].mode()) > 0 else 'N/A'
            })
        
        detailed_stats = pd.DataFrame(stats_data)
        processor.format_output(detailed_stats, "高发货量学校详细统计信息")
        
        # 发货量分布分析
        print(f"\n**发货量分布分析：**")
        volume_ranges = pd.cut(high_volume_schools['发货量'], 
                              bins=[500, 1000, 2000, 5000, float('inf')], 
                              labels=['500-1000件', '1000-2000件', '2000-5000件', '5000件以上'],
                              right=False)
        volume_distribution = volume_ranges.value_counts().sort_index()
        
        for range_name, count in volume_distribution.items():
            percentage = count / total_schools * 100
            print(f"- {range_name}: {count} 个学校 ({percentage:.1f}%)")
            
    else:
        print("没有发货量超过500件的学校")

if __name__ == "__main__":
    solve_question_06()
