"""
问题12：2021年所有校服中，哪个学校的校服销售额最高？具体数值是多少？
"""

from data_utils import create_processor

def solve_question_12():
    """解决问题12：销售额最高的学校"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
    # 按学校统计销售金额
    school_sales = processor.get_top_by_column(
        df, 
        group_by='学校', 
        agg_column='销售金额', 
        agg_func='sum',
        top_n=15  # 显示前15个学校
    )
    
    # 重命名列
    school_sales.columns = ['学校', '销售金额']
    school_sales['销售金额'] = school_sales['销售金额'].round(2)
    
    # 输出结果
    processor.format_output(school_sales, "各学校销售金额排名")
    
    # 输出最高的学校信息
    top_school = school_sales.iloc[0]
    print(f"\n**答案：{top_school['学校']} 的校服销售额最高，具体数值是 {top_school['销售金额']:.2f} 元**")
    
    # 额外统计信息 - 显示各学校的详细信息
    import pandas as pd
    stats_data = []
    for _, row in school_sales.head(10).iterrows():  # 只显示前10个的详细信息
        school = row['学校']
        school_data = df[df['学校'] == school]
        stats_data.append({
            '学校': school,
            '销售金额': row['销售金额'],
            '订单数量': len(school_data),
            '发货数量': school_data['数量'].sum(),
            '平均单价': school_data['折后单价'].mean().round(2),
            '平均折扣率(%)': school_data['折扣率'].mean().round(2),
            '小类数量': school_data['小类'].nunique(),
            '主要季节': school_data['季节'].mode().iloc[0] if len(school_data['季节'].mode()) > 0 else 'N/A',
            '主要小类': school_data['小类'].mode().iloc[0] if len(school_data['小类'].mode()) > 0 else 'N/A'
        })
    
    detailed_stats = pd.DataFrame(stats_data)
    processor.format_output(detailed_stats, "前10个学校详细统计信息")
    
    # 学校销售分析
    total_sales = school_sales['销售金额'].sum()
    print(f"\n**学校销售分析：**")
    print(f"- 前15个学校总销售金额：{total_sales:.2f} 元")
    print(f"- 最高销售额学校：{top_school['学校']} ({top_school['销售金额']:.2f} 元)")
    print(f"- 第15名销售额：{school_sales.iloc[-1]['销售金额']:.2f} 元（{school_sales.iloc[-1]['学校']}）")
    print(f"- 最高与最低差距：{(top_school['销售金额'] - school_sales.iloc[-1]['销售金额']):.2f} 元")

if __name__ == "__main__":
    solve_question_12()
