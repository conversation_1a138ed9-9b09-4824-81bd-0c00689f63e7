"""
问题20：2021年所有校服中，哪个仓库的退货率（退货件数/总发货件数）最低？具体数值是多少？
"""

from data_utils import create_processor

def solve_question_20():
    """解决问题20：2021年所有校服中，哪个仓库的退货率（退货件数/总发货件数）最低？具体数值是多少？"""

    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()

    # 计算各仓库的退货率
    warehouse_return_rate = processor.calculate_return_rate_by_quantity(df, '仓库名称')

    # 筛选有足够样本量的仓库（至少100件）
    warehouse_return_rate = warehouse_return_rate[warehouse_return_rate['总发货件数'] >= 100]

    # 按退货率升序排序（最低的在前）
    warehouse_return_rate = warehouse_return_rate.sort_values('退货率(%)', ascending=True)

    # 输出结果
    processor.format_output(warehouse_return_rate, "各仓库退货率排名（从低到高，最少100件样本）")

    # 输出退货率最低的仓库信息
    if len(warehouse_return_rate) > 0:
        best_warehouse = warehouse_return_rate.iloc[0]
        print(f"\n**答案：{best_warehouse['仓库名称']} 仓库的退货率最低，具体数值是 {best_warehouse['退货率(%)']}%**")

        # 详细分析
        print(f"\n**详细分析：**")
        print(f"- 该仓库总发货件数：{best_warehouse['总发货件数']} 件")
        print(f"- 退货件数：{best_warehouse['退货件数']} 件")
        print(f"- 退货率：{best_warehouse['退货率(%)']}%")
    else:
        print("\n**没有找到符合条件的仓库（至少100件样本）**")
        return

    # 额外统计信息 - 显示各仓库的详细信息
    import pandas as pd
    stats_data = []
    for _, row in warehouse_return_rate.head(10).iterrows():  # 只显示前10个的详细信息
        warehouse = row['仓库名称']
        warehouse_data = df[df['仓库名称'] == warehouse]
        stats_data.append({
            '仓库名称': warehouse,
            '退货率(%)': row['退货率(%)'],
            '总发货件数': row['总发货件数'],
            '退货件数': row['退货件数'],
            '销售金额': warehouse_data['销售金额'].sum().round(2),
            '订单数量': len(warehouse_data),
            '平均单价': warehouse_data['折后单价'].mean().round(2),
            '学校数量': warehouse_data['学校'].nunique(),
            '小类数量': warehouse_data['小类'].nunique()
        })

    detailed_stats = pd.DataFrame(stats_data)
    processor.format_output(detailed_stats, "前10个仓库详细统计信息")

    # 退货率分析
    print(f"\n**退货率分析：**")
    print(f"- 参与统计仓库数：{len(warehouse_return_rate)} 个")
    print(f"- 最低退货率：{warehouse_return_rate.iloc[0]['退货率(%)']}%")
    print(f"- 最高退货率：{warehouse_return_rate.iloc[-1]['退货率(%)']}%")
    print(f"- 平均退货率：{warehouse_return_rate['退货率(%)'].mean():.2f}%")

if __name__ == "__main__":
    solve_question_20()
