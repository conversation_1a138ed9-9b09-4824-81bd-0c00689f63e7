"""
问题29：2021年春秋季校服的销售金额总计是多少？其中销售金额超过平均值的订单有多少个？
"""

from data_utils import create_processor
import pandas as pd

def solve_question_29():
    """解决问题29：2021年春秋季校服的销售金额总计是多少？其中销售金额超过平均值的订单有多少个？"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
# 春秋季校服销售金额总计及超平均值订单数
    spring_autumn_data = df[df['季节'] == '春秋']
    total_amount = spring_autumn_data['销售金额'].sum()
    avg_amount = spring_autumn_data['销售金额'].mean()
    above_avg_orders = len(spring_autumn_data[spring_autumn_data['销售金额'] > avg_amount])
    
    result = pd.DataFrame({
        '季节': ['春秋'],
        '总销售金额': [round(total_amount, 2)],
        '平均订单金额': [round(avg_amount, 2)],
        '超过平均值订单数': [above_avg_orders],
        '总订单数': [len(spring_autumn_data)],
        '超平均值占比(%)': [round(above_avg_orders/len(spring_autumn_data)*100, 2)]
    })
    
    processor.format_output(result, "春秋季校服销售金额统计")
    print(f"\n**答案：春秋季校服销售金额总计 {total_amount:.2f} 元，超过平均值的订单有 {above_avg_orders} 个**")

if __name__ == "__main__":
    solve_question_29()
