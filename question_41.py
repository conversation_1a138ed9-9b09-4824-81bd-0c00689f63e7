"""
问题41：列出海淀进修学校2021年10月的订单发货时效，发货时效为发货时间 - 付款时间。
"""

from data_utils import create_processor

def solve_question_41():
    """解决问题41：列出海淀进修学校2021年10月的订单发货时效，发货时效为发货时间 - 付款时间。"""

    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()

    # 筛选海淀进修学校2021年10月的数据
    oct_data = processor.filter_by_month(df, '付款时间', 2021, 10)
    haidian_oct_data = oct_data[oct_data['学校'] == '海淀进修']

    if len(haidian_oct_data) == 0:
        print("**没有找到海淀进修学校2021年10月的数据**")
        return

    # 计算发货时效
    haidian_oct_data = processor.calculate_delivery_time(haidian_oct_data)

    # 过滤掉无效的发货时效数据
    valid_data = haidian_oct_data.dropna(subset=['发货时效(天)'])
    valid_data = valid_data[valid_data['发货时效(天)'] >= 0]

    if len(valid_data) == 0:
        print("**没有找到有效的发货时效数据**")
        return

    # 选择需要显示的列
    result_columns = ['订单号', '商品名称', '小类', '发货时效(天)', '销售金额', '数量', '付款时间', '发货时间']
    result_df = valid_data[result_columns].copy()
    result_df['发货时效(天)'] = result_df['发货时效(天)'].round(2)
    result_df['销售金额'] = result_df['销售金额'].round(2)

    # 按发货时效排序
    result_df = result_df.sort_values('发货时效(天)', ascending=True)

    # 输出结果
    processor.format_output(result_df, "海淀进修学校2021年10月订单发货时效")

    # 统计分析
    avg_delivery_time = valid_data['发货时效(天)'].mean()
    min_delivery_time = valid_data['发货时效(天)'].min()
    max_delivery_time = valid_data['发货时效(天)'].max()
    median_delivery_time = valid_data['发货时效(天)'].median()

    print(f"\n**发货时效统计分析：**")
    print(f"- 总订单数：{len(valid_data)} 个")
    print(f"- 平均发货时效：{avg_delivery_time:.2f} 天")
    print(f"- 最短发货时效：{min_delivery_time:.2f} 天")
    print(f"- 最长发货时效：{max_delivery_time:.2f} 天")
    print(f"- 发货时效中位数：{median_delivery_time:.2f} 天")

    # 时效分布分析
    import pandas as pd
    time_ranges = [
        (0, 1, "1天内"),
        (1, 2, "1-2天"),
        (2, 3, "2-3天"),
        (3, 5, "3-5天"),
        (5, float('inf'), "5天以上")
    ]

    distribution_data = []
    for min_days, max_days, label in time_ranges:
        if max_days == float('inf'):
            count = len(valid_data[valid_data['发货时效(天)'] >= min_days])
        else:
            count = len(valid_data[(valid_data['发货时效(天)'] >= min_days) &
                                 (valid_data['发货时效(天)'] < max_days)])
        percentage = (count / len(valid_data) * 100) if len(valid_data) > 0 else 0
        distribution_data.append({
            '时效范围': label,
            '订单数': count,
            '占比(%)': round(percentage, 2)
        })

    distribution_df = pd.DataFrame(distribution_data)
    processor.format_output(distribution_df, "发货时效分布")

if __name__ == "__main__":
    solve_question_41()
