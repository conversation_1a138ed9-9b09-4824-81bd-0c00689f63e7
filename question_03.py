"""
问题3：2021年各季节校服的销售额占比分别是多少？
"""

from data_utils import create_processor

def solve_question_03():
    """解决问题3：各季节校服销售额占比"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
    # 计算各季节销售额占比
    season_percentage = processor.calculate_percentage(
        df, 
        group_by='季节', 
        value_column='销售金额'
    )
    
    # 重命名列
    season_percentage.columns = ['季节', '销售金额', '占比(%)']
    season_percentage['销售金额'] = season_percentage['销售金额'].round(2)
    
    # 输出结果
    processor.format_output(season_percentage, "各季节校服销售额占比")
    
    # 详细统计信息
    total_sales = season_percentage['销售金额'].sum()
    print(f"\n**总体统计：**")
    print(f"- 总销售金额：{total_sales:.2f} 元")
    print(f"- 季节数量：{len(season_percentage)} 个")
    
    # 显示各季节的详细信息
    import pandas as pd
    stats_data = []
    for _, row in season_percentage.iterrows():
        season = row['季节']
        season_data = df[df['季节'] == season]
        stats_data.append({
            '季节': season,
            '销售金额': row['销售金额'],
            '占比(%)': row['占比(%)'],
            '订单数量': len(season_data),
            '发货数量': season_data['数量'].sum(),
            '平均单价': season_data['折后单价'].mean().round(2),
            '平均折扣率(%)': season_data['折扣率'].mean().round(2),
            '小类数量': season_data['小类'].nunique(),
            '学校数量': season_data['学校'].nunique()
        })
    
    detailed_stats = pd.DataFrame(stats_data)
    processor.format_output(detailed_stats, "各季节详细统计信息")
    
    # 输出占比最高的季节
    top_season = season_percentage.iloc[0]
    print(f"\n**答案：{top_season['季节']} 季节的校服销售额占比最高，具体占比是 {top_season['占比(%)']}%**")

if __name__ == "__main__":
    solve_question_03()
