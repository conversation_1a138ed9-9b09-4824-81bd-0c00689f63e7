"""
问题37：列出徐水仓2021年Q1棉服的销售数量及订单数
"""

from data_utils import create_processor

def solve_question_37():
    """解决问题37：列出徐水仓2021年Q1棉服的销售数量及订单数"""

    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()

    # 筛选2021年Q1数据
    q1_data = processor.filter_by_quarter(df, '付款时间', 2021, 1)

    # 筛选徐水仓和棉服的数据
    # 先检查仓库名称
    warehouses = df['仓库名称'].unique()
    xushui_warehouse = None
    for warehouse in warehouses:
        if '徐水' in str(warehouse):
            xushui_warehouse = warehouse
            break

    if xushui_warehouse is None:
        print("**没有找到徐水仓**")
        print(f"可用仓库：{list(warehouses)}")
        return

    # 筛选徐水仓棉服数据
    target_data = q1_data[(q1_data['仓库名称'] == xushui_warehouse) & (q1_data['小类'] == '棉服')]

    if len(target_data) == 0:
        print(f"**没有找到{xushui_warehouse}在2021年Q1的棉服数据**")
        return

    # 统计销售数量和订单数
    total_quantity = target_data['数量'].sum()
    order_count = len(target_data)
    total_amount = target_data['销售金额'].sum()

    # 创建结果汇总
    import pandas as pd
    summary_data = pd.DataFrame({
        '仓库': [xushui_warehouse],
        '时间': ['2021年Q1'],
        '小类': ['棉服'],
        '销售数量': [total_quantity],
        '订单数': [order_count],
        '销售金额': [round(total_amount, 2)],
        '平均每单数量': [round(total_quantity/order_count, 1) if order_count > 0 else 0],
        '平均每单金额': [round(total_amount/order_count, 2) if order_count > 0 else 0]
    })

    # 输出结果
    processor.format_output(summary_data, "徐水仓2021年Q1棉服销售汇总")

    print(f"\n**答案：徐水仓2021年Q1棉服销售数量为 {total_quantity} 件，订单数为 {order_count} 个**")

if __name__ == "__main__":
    solve_question_37()
