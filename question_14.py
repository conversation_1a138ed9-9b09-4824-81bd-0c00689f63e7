"""
问题14：列出2021年所有发货量超过500件的学校，包含学校名称和发货量
"""

from data_utils import create_processor

def solve_question_14():
    """解决问题14：发货量超过500件的学校"""
    
    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()
    
    # 按学校统计发货量
    school_quantity = processor.get_top_by_column(
        df, 
        group_by='学校', 
        agg_column='数量', 
        agg_func='sum',
        top_n=100  # 先获取更多数据用于筛选
    )
    
    # 重命名列
    school_quantity.columns = ['学校', '发货量']
    
    # 筛选发货量超过500件的学校
    filtered_schools = school_quantity[school_quantity['发货量'] > 500]
    
    # 输出结果
    processor.format_output(filtered_schools, "发货量超过500件的学校")
    
    # 统计信息
    total_qualified = len(filtered_schools)
    print(f"\n**统计信息：**")
    print(f"- 发货量超过500件的学校总数：{total_qualified} 个")
    
    if len(filtered_schools) > 0:
        print(f"- 最高发货量：{filtered_schools.iloc[0]['发货量']} 件（{filtered_schools.iloc[0]['学校']}）")
        print(f"- 最低发货量：{filtered_schools.iloc[-1]['发货量']} 件（{filtered_schools.iloc[-1]['学校']}）")
        print(f"- 平均发货量：{filtered_schools['发货量'].mean():.0f} 件")
        print(f"- 总发货量：{filtered_schools['发货量'].sum()} 件")
    
    # 额外统计信息 - 显示各学校的详细信息
    import pandas as pd
    stats_data = []
    for _, row in filtered_schools.head(15).iterrows():  # 显示前15个的详细信息
        school = row['学校']
        school_data = df[df['学校'] == school]
        stats_data.append({
            '学校': school,
            '发货量': row['发货量'],
            '销售金额': school_data['销售金额'].sum().round(2),
            '订单数量': len(school_data),
            '平均单价': school_data['折后单价'].mean().round(2),
            '小类数量': school_data['小类'].nunique(),
            '主要季节': school_data['季节'].mode().iloc[0] if len(school_data['季节'].mode()) > 0 else 'N/A',
            '主要小类': school_data['小类'].mode().iloc[0] if len(school_data['小类'].mode()) > 0 else 'N/A'
        })
    
    detailed_stats = pd.DataFrame(stats_data)
    processor.format_output(detailed_stats, "前15个学校详细统计信息")

if __name__ == "__main__":
    solve_question_14()
