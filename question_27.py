"""
问题27：2021年2月所有校服中，哪个SKU的销售额超过平均值的订单数量最多？具体数量是多少？
"""

from data_utils import create_processor

def solve_question_27():
    """解决问题27：2021年2月所有校服中，哪个SKU的销售额超过平均值的订单数量最多？具体数量是多少？"""

    # 创建数据处理器
    processor = create_processor()
    df = processor.get_merged_data()

    # 筛选2021年2月的数据
    feb_data = processor.filter_by_month(df, '付款时间', 2021, 2)

    if len(feb_data) == 0:
        print("**没有找到2021年2月的数据**")
        return

    print(f"2021年2月数据量：{len(feb_data)} 条")

    # 获取2月数据中超过平均值的订单统计
    sku_above_avg = processor.get_orders_above_average(feb_data, 'SKU', '销售金额')

    # 筛选有足够样本量的SKU（至少3个订单）
    sku_above_avg = sku_above_avg[sku_above_avg['总订单数'] >= 3]

    # 按超过平均值订单数排序
    sku_above_avg = sku_above_avg.sort_values('超过平均值订单数', ascending=False)

    # 输出结果
    processor.format_output(sku_above_avg.head(15), "2021年2月各SKU销售额超过平均值订单数排名（前15名，最少3个订单）")

    # 输出超过平均值订单数最多的SKU信息
    if len(sku_above_avg) > 0:
        top_sku = sku_above_avg.iloc[0]
        print(f"\n**答案：2021年2月，SKU {top_sku['SKU']} 的销售额超过平均值的订单数量最多，具体数量是 {top_sku['超过平均值订单数']} 个**")

        # 获取该SKU的详细信息
        sku_data = feb_data[feb_data['SKU'] == top_sku['SKU']]
        if len(sku_data) > 0:
            print(f"- 商品名称：{sku_data['商品名称'].iloc[0]}")
            print(f"- 所属学校：{sku_data['学校'].iloc[0]}")
            print(f"- 小类：{sku_data['小类'].iloc[0]}")
            print(f"- 季节：{sku_data['季节'].iloc[0]}")
            print(f"- 总订单数：{top_sku['总订单数']} 个")
            print(f"- 占比：{top_sku['占比(%)']}%")
    else:
        print("\n**没有找到符合条件的SKU（至少3个订单）**")
        return

    # 计算2月平均销售金额
    feb_avg = feb_data['销售金额'].mean()
    print(f"\n**2021年2月平均销售金额：{feb_avg:.2f} 元**")

    # 额外统计信息 - 显示各SKU的详细信息
    import pandas as pd
    stats_data = []
    for _, row in sku_above_avg.head(10).iterrows():  # 只显示前10个的详细信息
        sku = row['SKU']
        sku_data = feb_data[feb_data['SKU'] == sku]
        above_avg_data = sku_data[sku_data['销售金额'] > feb_avg]

        stats_data.append({
            'SKU': sku,
            '超过平均值订单数': row['超过平均值订单数'],
            '总订单数': row['总订单数'],
            '占比(%)': row['占比(%)'],
            '超平均值订单平均金额': above_avg_data['销售金额'].mean().round(2) if len(above_avg_data) > 0 else 0,
            '总销售金额': sku_data['销售金额'].sum().round(2),
            '平均单价': sku_data['折后单价'].mean().round(2),
            '学校': sku_data['学校'].iloc[0] if len(sku_data) > 0 else 'N/A',
            '小类': sku_data['小类'].iloc[0] if len(sku_data) > 0 else 'N/A'
        })

    detailed_stats = pd.DataFrame(stats_data)
    processor.format_output(detailed_stats, "前10个SKU详细统计信息")

    # 2月数据分析
    print(f"\n**2021年2月数据分析：**")
    print(f"- 2月总订单数：{len(feb_data)} 个")
    print(f"- 2月总销售金额：{feb_data['销售金额'].sum():.2f} 元")
    print(f"- 2月平均订单金额：{feb_avg:.2f} 元")
    print(f"- 超过平均值的订单数：{len(feb_data[feb_data['销售金额'] > feb_avg])} 个")
    print(f"- 参与统计的SKU数：{len(sku_above_avg)} 个")

if __name__ == "__main__":
    solve_question_27()
